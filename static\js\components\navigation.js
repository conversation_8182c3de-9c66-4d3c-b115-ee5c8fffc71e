import { DOMUtils } from '../utils/dom.js';
import { Logger } from '../utils/logger.js';

// 导航组件类
export class NavigationComponent {
    constructor(options = {}) {
        this.currentView = 'api-test'; // 默认视图
        this.logger = options.logger || new Logger();
        this.onViewChange = options.onViewChange || (() => {});
        
        this.init();
    }

    // 初始化导航组件
    init() {
        this.bindEvents();
        this.updateActiveNavItem();
        this.logger.info('导航组件初始化完成');
    }

    // 绑定事件
    bindEvents() {
        // 导航菜单点击事件
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const viewName = item.dataset.view;
                if (viewName) {
                    this.switchView(viewName);
                }
            });
        });

        // 键盘快捷键支持
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case '1':
                        e.preventDefault();
                        this.switchView('api-test');
                        break;
                    case '2':
                        e.preventDefault();
                        this.switchView('log-viewer');
                        break;
                }
            }
        });
    }

    // 切换视图
    switchView(viewName) {
        if (viewName === this.currentView) {
            return; // 已经是当前视图
        }

        try {
            // 隐藏当前视图
            this.hideCurrentView();

            // 显示新视图
            this.showView(viewName);

            // 更新当前视图
            this.currentView = viewName;

            // 更新导航状态
            this.updateActiveNavItem();

            // 触发视图变化回调
            this.onViewChange(viewName);

            this.logger.info(`切换到视图: ${viewName}`);

        } catch (error) {
            this.logger.error(`切换视图失败: ${viewName}`, error);
        }
    }

    // 隐藏当前视图
    hideCurrentView() {
        const currentViewElement = document.getElementById(`${this.currentView}-view`);
        if (currentViewElement) {
            DOMUtils.removeClass(currentViewElement, 'active');
            DOMUtils.addClass(currentViewElement, 'hidden');
        }
    }

    // 显示视图
    showView(viewName) {
        const viewElement = document.getElementById(`${viewName}-view`);
        if (viewElement) {
            DOMUtils.removeClass(viewElement, 'hidden');
            DOMUtils.addClass(viewElement, 'active');
        } else {
            throw new Error(`视图元素未找到: ${viewName}-view`);
        }
    }

    // 更新活跃导航项
    updateActiveNavItem() {
        // 移除所有活跃状态
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            DOMUtils.removeClass(item, 'active');
        });

        // 添加当前视图的活跃状态
        const activeNavItem = document.querySelector(`[data-view="${this.currentView}"]`);
        if (activeNavItem) {
            DOMUtils.addClass(activeNavItem, 'active');
        }
    }

    // 获取当前视图
    getCurrentView() {
        return this.currentView;
    }

    // 获取所有可用视图
    getAvailableViews() {
        const navItems = document.querySelectorAll('.nav-item[data-view]');
        return Array.from(navItems).map(item => ({
            name: item.dataset.view,
            title: item.querySelector('.nav-text')?.textContent || item.dataset.view,
            icon: item.querySelector('.nav-icon')?.textContent || ''
        }));
    }

    // 添加新视图
    addView(viewConfig) {
        const { name, title, icon, element } = viewConfig;

        // 创建导航项
        const navItem = DOMUtils.createElement('li', {
            className: 'nav-item',
            attributes: { 'data-view': name }
        });

        const navIcon = DOMUtils.createElement('span', {
            className: 'nav-icon',
            textContent: icon || '📄'
        });

        const navText = DOMUtils.createElement('span', {
            className: 'nav-text',
            textContent: title
        });

        navItem.appendChild(navIcon);
        navItem.appendChild(navText);

        // 添加到导航菜单
        const navMenu = document.querySelector('.nav-menu');
        if (navMenu) {
            navMenu.appendChild(navItem);
        }

        // 添加视图元素到内容区域
        if (element) {
            element.id = `${name}-view`;
            element.className = 'content-view hidden';
            
            const contentArea = document.querySelector('.right-content');
            if (contentArea) {
                contentArea.appendChild(element);
            }
        }

        // 绑定点击事件
        navItem.addEventListener('click', (e) => {
            e.preventDefault();
            this.switchView(name);
        });

        this.logger.info(`添加新视图: ${name}`);
    }

    // 移除视图
    removeView(viewName) {
        // 移除导航项
        const navItem = document.querySelector(`[data-view="${viewName}"]`);
        if (navItem) {
            navItem.remove();
        }

        // 移除视图元素
        const viewElement = document.getElementById(`${viewName}-view`);
        if (viewElement) {
            viewElement.remove();
        }

        // 如果移除的是当前视图，切换到默认视图
        if (this.currentView === viewName) {
            this.switchView('api-test');
        }

        this.logger.info(`移除视图: ${viewName}`);
    }

    // 设置视图标题
    setViewTitle(viewName, title) {
        const navItem = document.querySelector(`[data-view="${viewName}"] .nav-text`);
        if (navItem) {
            DOMUtils.setText(navItem, title);
        }
    }

    // 设置视图图标
    setViewIcon(viewName, icon) {
        const navIcon = document.querySelector(`[data-view="${viewName}"] .nav-icon`);
        if (navIcon) {
            DOMUtils.setText(navIcon, icon);
        }
    }

    // 禁用视图
    disableView(viewName) {
        const navItem = document.querySelector(`[data-view="${viewName}"]`);
        if (navItem) {
            DOMUtils.addClass(navItem, 'disabled');
            navItem.style.pointerEvents = 'none';
            navItem.style.opacity = '0.5';
        }
    }

    // 启用视图
    enableView(viewName) {
        const navItem = document.querySelector(`[data-view="${viewName}"]`);
        if (navItem) {
            DOMUtils.removeClass(navItem, 'disabled');
            navItem.style.pointerEvents = '';
            navItem.style.opacity = '';
        }
    }

    // 添加视图徽章（显示数字或状态）
    addViewBadge(viewName, content, className = 'badge') {
        const navItem = document.querySelector(`[data-view="${viewName}"]`);
        if (navItem) {
            // 移除现有徽章
            const existingBadge = navItem.querySelector('.badge');
            if (existingBadge) {
                existingBadge.remove();
            }

            // 添加新徽章
            const badge = DOMUtils.createElement('span', {
                className: className,
                textContent: content
            });

            navItem.appendChild(badge);
        }
    }

    // 移除视图徽章
    removeViewBadge(viewName) {
        const navItem = document.querySelector(`[data-view="${viewName}"]`);
        if (navItem) {
            const badge = navItem.querySelector('.badge');
            if (badge) {
                badge.remove();
            }
        }
    }

    // 获取视图历史
    getViewHistory() {
        return this.viewHistory || [];
    }

    // 返回上一个视图
    goBack() {
        const history = this.getViewHistory();
        if (history.length > 1) {
            const previousView = history[history.length - 2];
            this.switchView(previousView);
        }
    }

    // 刷新当前视图
    refreshCurrentView() {
        const currentView = this.currentView;
        this.hideCurrentView();
        
        // 短暂延迟后重新显示
        setTimeout(() => {
            this.showView(currentView);
            this.onViewChange(currentView, { refresh: true });
        }, 100);
    }

    // 销毁导航组件
    destroy() {
        // 移除事件监听器
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.removeEventListener('click', this.handleNavClick);
        });

        document.removeEventListener('keydown', this.handleKeydown);

        this.logger.info('导航组件已销毁');
    }
}