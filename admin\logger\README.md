# Logger 日志管理模块

这个模块提供了完整的日志文件管理功能，包括日志文件读取、实时监控和性能指标。

## 文件说明

- `config.go` - 配置管理，支持从INI文件加载配置
- `interfaces.go` - 定义了日志读取、文件监控等接口
- `logs.go` - 模块初始化和全局服务管理
- `models.go` - 数据模型定义，包括日志文件、内容、性能指标等结构
- `service.go` - 核心服务实现，提供并发安全的日志读取功能

## 主要功能

### 日志文件管理
- 获取日志文件列表
- 读取日志文件内容（支持分页）
- 实时监控日志文件变化
- 增量读取新增内容

### 性能优化
- 并发控制（限制同时读取的文件数量）
- 内存监控（防止内存溢出）
- 速率限制（防止API滥用）
- 大文件优化处理

### 安全特性
- 文件路径验证（防止路径遍历攻击）
- 文件扩展名白名单
- 访问日志记录
- 安全事件监控

## 使用示例

```go
import loggerPkg "jackpot/admin/logger"

// 创建日志读取服务
service := loggerPkg.NewLogReaderService("./logs", 3, 512)

// 获取日志文件列表
files, err := service.GetLogFiles()

// 读取日志内容
content, err := service.ReadLogContent("app.log", 100, 0)

// 实时监控
tailData, err := service.TailLogFile("app.log", 50, time.Now())
```