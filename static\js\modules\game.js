import { WebSocketManager } from '../core/websocket.js';
import { DOMUtils } from '../utils/dom.js';
import { Logger } from '../utils/logger.js';
import { Helpers } from '../utils/helpers.js';
import { CONSTANTS } from '../config/constants.js';
import { InterfaceFieldManager } from '../components/field-manager.js';

// 游戏接口模式模块
export class GameModule {
    constructor(options = {}) {
        this.wsManager = options.wsManager;
        this.logger = options.logger || new Logger();
        this.onResult = options.onResult || (() => {});
        
        this.fieldManager = null;
        this.currentUserId = 1001;
        this.currentCostType = 100;
        
        this.init();
    }

    // 初始化游戏模块
    init() {
        this.initializeFieldManager();
        this.bindEvents();
        this.initializeForm();
        this.logger.debug('游戏模块初始化完成');
    }

    // 初始化字段管理器
    initializeFieldManager() {
        const elements = {
            apiTypeEl: DOMUtils.getElementById('apiType'),
            repeatEl: DOMUtils.getElementById('repeat'),
            teamEl: DOMUtils.getElementById('team'),
            jyEl: DOMUtils.getElementById('jy')
        };

        this.fieldManager = new InterfaceFieldManager(elements, {
            onFieldChange: (interfaceId, config) => {
                this.handleFieldChange(interfaceId, config);
            },
            logger: this.logger
        });
    }

    // 绑定事件
    bindEvents() {
        // 接口类型变化事件
        const apiTypeSelect = DOMUtils.getElementById('apiType');
        if (apiTypeSelect) {
            apiTypeSelect.addEventListener('change', (e) => {
                this.fieldManager.handleInterfaceChange(e.target.value);
            });
        }

        // 用户ID变化事件
        const userIdInput = DOMUtils.getElementById('userId');
        if (userIdInput) {
            userIdInput.addEventListener('change', (e) => {
                this.currentUserId = parseInt(e.target.value) || 1001;
            });
        }

        // 成本类型变化事件
        const costTypeSelect = DOMUtils.getElementById('costType');
        if (costTypeSelect) {
            costTypeSelect.addEventListener('change', (e) => {
                this.currentCostType = parseInt(e.target.value) || 100;
            });
        }

        // 发送按钮事件
        const sendBtn = DOMUtils.getElementById('sendBtn');
        if (sendBtn) {
            sendBtn.addEventListener('click', () => {
                this.handleSend();
            });
        }

        // 快速测试按钮事件
        const quickTestBtn = DOMUtils.getElementById('quickTestBtn');
        if (quickTestBtn) {
            quickTestBtn.addEventListener('click', () => {
                this.handleQuickTest();
            });
        }

        // 批量测试按钮事件
        const batchTestBtn = DOMUtils.getElementById('batchTestBtn');
        if (batchTestBtn) {
            batchTestBtn.addEventListener('click', () => {
                this.handleBatchTest();
            });
        }

        // 表单字段验证事件
        this.bindValidationEvents();
    }

    // 绑定验证事件
    bindValidationEvents() {
        const fields = ['userId', 'repeat', 'team', 'jy'];
        
        fields.forEach(fieldId => {
            const field = DOMUtils.getElementById(fieldId);
            if (field) {
                field.addEventListener('blur', () => {
                    this.validateField(fieldId);
                });
                
                field.addEventListener('input', () => {
                    this.clearFieldError(fieldId);
                });
            }
        });
    }

    // 初始化表单
    initializeForm() {
        // 设置默认值
        const userIdInput = DOMUtils.getElementById('userId');
        if (userIdInput && !userIdInput.value) {
            userIdInput.value = this.currentUserId;
        }

        const costTypeSelect = DOMUtils.getElementById('costType');
        if (costTypeSelect && !costTypeSelect.value) {
            costTypeSelect.value = this.currentCostType;
        }

        // 初始化接口字段
        const apiTypeSelect = DOMUtils.getElementById('apiType');
        if (apiTypeSelect) {
            this.fieldManager.handleInterfaceChange(apiTypeSelect.value || '1');
        }
    }

    // 处理字段变化
    handleFieldChange(interfaceId, config) {
        this.logger.debug(`接口字段变化: ${interfaceId}`, config);
        
        // 可以在这里添加特定接口的额外处理逻辑
        switch (interfaceId) {
            case 9:
                this.handleInterface9Change(config);
                break;
            case 21:
                this.handleInterface21Change(config);
                break;
        }
    }

    // 处理接口9的特殊逻辑
    handleInterface9Change(config) {
        // 接口9的特殊处理已在 InterfaceFieldManager 中实现
        this.logger.debug('接口9特殊逻辑处理');
    }

    // 处理接口21的特殊逻辑
    handleInterface21Change(config) {
        // 接口21：仙丹开鼎，repeat表示仙丹数量
        const repeatLabel = this.fieldManager.getFieldLabel('repeat');
        if (repeatLabel) {
            DOMUtils.setText(repeatLabel, '仙丹数量:');
        }
    }

    // 处理发送请求
    async handleSend() {
        try {
            // 验证表单
            const validation = this.validateForm();
            if (!validation.valid) {
                this.showValidationErrors(validation.errors);
                return;
            }

            // 显示加载状态
            this.setSendButtonState(true);

            // 发送WebSocket消息
            const result = await this.sendGameRequest(validation.data);
            
            // 显示结果
            this.onResult(result);
            this.logger.debug('游戏请求发送成功', result);

        } catch (error) {
            this.logger.error('游戏请求发送失败:', error.message);
            this.onResult({ error: error.message });
        } finally {
            this.setSendButtonState(false);
        }
    }

    // 发送游戏请求
    async sendGameRequest(data) {
        if (!this.wsManager || !this.wsManager.isConnected) {
            throw new Error('WebSocket未连接');
        }

        const messageType = parseInt(data.messageType) || 1;
        const userId = parseInt(data.userId) || 1001;
        const cost = parseInt(data.costType) || 100;
        const repeat = parseInt(data.repeat) || 1;
        const team = parseInt(data.team) || 0;
        const jy = parseInt(data.jy) || 0;

        // 根据test/main.go的逻辑构建不同接口的消息数据
        let messageData;

        switch (messageType) {
            case 1: // 普通怪
            case 4: // 补充奖池 (当team=1时为个人模式)
            case 11: // 内丹探索
            case 20: // 仙丹掉落
            case 22: // 接口22
                messageData = {
                    uid: String(userId),
                    cost: cost,
                    repeat: repeat,
                    team: team,
                    jy: jy
                };
                break;

            case 3: // 多人请求 (已废弃，但保留逻辑)
                const users = {};
                users["A"] = [];
                users["B"] = [];
                users["C"] = [];
                users["D"] = [];
                users["S"] = [];
                
                for (let i = 0; i < repeat; i++) {
                    const uid = String(100000 + i);
                    const groupKeys = ["A", "B", "C", "D"];
                    const k = groupKeys[Math.floor(Math.random() * 4)];
                    
                    const maxUsers = cost === 100 ? 100 : (cost === 1000 ? 25 : 5);
                    if (users[k].length < maxUsers) {
                        users[k].push(uid);
                    }
                    
                    if (team === 1 && users["S"].length < 1) {
                        users["S"].push(uid);
                    }
                }
                
                messageData = {
                    users: users,
                    cost: cost
                };
                break;

            case 5: // 游戏局数请求 (已废弃)
                messageData = {
                    uid: String(userId),
                    cost: cost,
                    games: repeat
                };
                break;

            case 6: // 地图请求 (已废弃)
                messageData = {
                    uid: String(userId),
                    map: cost,
                    cost: cost * repeat,
                    multi: team
                };
                break;

            case 7: // 排行榜请求 (已废弃)
            case 8: // 排行榜请求 (已废弃)
                messageData = {
                    id: team
                };
                break;

            case 9: // 宝物开奖
                let uid9, counts9;
                if (team === 1) { // 手动开奖
                    const itemCount = cost === 0 ? 24 : 8;
                    counts9 = new Array(itemCount).fill(0);
                    let total = repeat;
                    for (let i = 0; i < itemCount - 1; i++) {
                        const randomCount = Math.floor(Math.random() * (total + 1));
                        counts9[i] = randomCount;
                        total -= randomCount;
                    }
                    counts9[itemCount - 1] = total;
                    uid9 = String(userId);
                } else { // 自动开奖
                    // 使用全局统计数据 (这里简化处理)
                    counts9 = cost === 0 ? new Array(24).fill(0) : new Array(8).fill(0);
                    uid9 = "-1";
                }
                
                const oid9 = String(Math.floor(Math.random() * 90000) + 10000);
                messageData = {
                    oid: oid9,
                    uid: uid9,
                    map: cost,
                    counts: counts9
                };
                break;

            case 10: // 已废弃，使用类似11的逻辑
                messageData = {
                    uid: String(userId),
                    cost: cost,
                    repeat: repeat,
                    jy: jy
                };
                break;

            case 12: // 已废弃，类似9的逻辑
                const counts12 = new Array(8).fill(0);
                let total12 = repeat;
                for (let i = 0; i < 7; i++) {
                    const randomCount = Math.floor(Math.random() * (total12 + 1));
                    counts12[i] = randomCount;
                    total12 -= randomCount;
                }
                counts12[7] = total12;
                
                const oid12 = String(Math.floor(Math.random() * 90000) + 10000);
                const rejackpot = repeat * cost;
                
                messageData = {
                    oid: oid12,
                    uid: String(userId),
                    map: cost,
                    counts: counts12,
                    rejackpot: rejackpot
                };
                break;

            case 21: // 仙丹开鼎
                const counts21 = new Array(8).fill(0);
                let total21 = repeat;
                for (let i = 0; i < 7; i++) {
                    const randomCount = Math.floor(Math.random() * (total21 + 1));
                    counts21[i] = randomCount;
                    total21 -= randomCount;
                }
                counts21[7] = total21;
                
                const oid21 = String(Math.floor(Math.random() * 90000) + 10000);
                
                messageData = {
                    oid: oid21,
                    uid: String(userId),
                    map: cost,
                    counts: counts21
                };
                break;

            default:
                // 默认使用基础格式
                messageData = {
                    uid: String(userId),
                    cost: cost,
                    repeat: repeat,
                    team: team,
                    jy: jy
                };
                break;
        }

        // 发送WebSocket消息
        const message = {
            type: messageType,
            flag: userId,
            ...messageData
        };
        
        await this.wsManager.send(message);
        
        return {
            success: true,
            message: '请求已发送',
            data: {
                messageType: messageType,
                userId: userId,
                requestData: messageData
            }
        };
    }

    // 验证表单
    validateForm() {
        const errors = [];
        const data = {};

        // 验证用户ID
        const userIdInput = DOMUtils.getElementById('userId');
        if (!userIdInput) {
            errors.push('用户ID输入框未找到');
        } else {
            const userIdValidation = Helpers.validateInteger(userIdInput.value, 1);
            if (!userIdValidation.valid) {
                errors.push(`用户ID: ${userIdValidation.error}`);
            } else {
                data.userId = userIdValidation.value;
            }
        }

        // 验证接口类型
        const apiTypeSelect = DOMUtils.getElementById('apiType');
        if (!apiTypeSelect) {
            errors.push('接口类型选择器未找到');
        } else {
            const messageType = parseInt(apiTypeSelect.value);
            if (isNaN(messageType)) {
                errors.push('请选择有效的接口类型');
            } else {
                data.messageType = messageType;
            }
        }

        // 验证成本类型
        const costTypeSelect = DOMUtils.getElementById('costType');
        if (!costTypeSelect) {
            errors.push('成本类型选择器未找到');
        } else {
            const costType = parseInt(costTypeSelect.value);
            if (!CONSTANTS.COST_TIERS.includes(costType)) {
                errors.push(`成本类型必须是: ${CONSTANTS.COST_TIERS.join(', ')}`);
            } else {
                data.costType = costType;
            }
        }

        // 验证字段值
        const fieldValidation = this.fieldManager.validateFieldValues();
        if (!fieldValidation.valid) {
            errors.push(...fieldValidation.errors);
        } else {
            Object.assign(data, fieldValidation.values);
        }

        return {
            valid: errors.length === 0,
            errors,
            data
        };
    }

    // 快速测试
    async handleQuickTest() {
        try {
            this.logger.system('开始快速测试...');
            
            // 使用当前表单数据进行快速测试
            const validation = this.validateForm();
            if (!validation.valid) {
                this.showValidationErrors(validation.errors);
                return;
            }

            // 发送5次请求
            const results = [];
            for (let i = 0; i < 5; i++) {
                try {
                    const result = await this.sendGameRequest(validation.data);
                    results.push(result);
                    await Helpers.delay(200); // 间隔200ms
                } catch (error) {
                    results.push({ error: error.message });
                }
            }

            this.onResult({
                type: 'quickTest',
                results: results,
                summary: {
                    total: results.length,
                    success: results.filter(r => r.success).length,
                    failed: results.filter(r => r.error).length
                }
            });

            this.logger.debug('快速测试详细结果', results);

        } catch (error) {
            this.logger.error('快速测试失败:', error.message);
            this.onResult({ error: error.message });
        }
    }

    // 批量测试
    async handleBatchTest() {
        try {
            this.logger.system('开始批量测试...');
            
            const batchConfig = this.getBatchTestConfig();
            const results = [];

            for (const config of batchConfig) {
                try {
                    const result = await this.sendGameRequest(config);
                    results.push({ config, result });
                    await Helpers.delay(100); // 间隔100ms
                } catch (error) {
                    results.push({ config, error: error.message });
                }
            }

            this.onResult({
                type: 'batchTest',
                results: results,
                summary: {
                    total: results.length,
                    success: results.filter(r => r.result && r.result.success).length,
                    failed: results.filter(r => r.error).length
                }
            });

            this.logger.debug('批量测试详细结果', results);

        } catch (error) {
            this.logger.error('批量测试失败:', error.message);
            this.onResult({ error: error.message });
        }
    }

    // 获取批量测试配置
    getBatchTestConfig() {
        const baseConfig = {
            userId: this.currentUserId,
            messageType: parseInt(DOMUtils.getElementById('apiType')?.value || '1'),
            costType: this.currentCostType,
            team: 0,
            jy: 0
        };

        // 生成不同的repeat值进行测试
        return [1, 5, 10].map(repeat => ({
            ...baseConfig,
            repeat
        }));
    }

    // 验证单个字段
    validateField(fieldId) {
        const field = DOMUtils.getElementById(fieldId);
        if (!field) return;

        let validation = { valid: true };

        switch (fieldId) {
            case 'userId':
                validation = Helpers.validateInteger(field.value, 1);
                break;

            case 'repeat':
                validation = Helpers.validateInteger(field.value, 1, 100);
                break;

            case 'team':
                validation = Helpers.validateInteger(field.value, 0, 10);
                break;

            case 'jy':
                validation = Helpers.validateInteger(field.value, 0, 1);
                break;
        }

        if (!validation.valid) {
            this.showFieldError(fieldId, validation.error);
        } else {
            this.clearFieldError(fieldId);
        }

        return validation.valid;
    }

    // 显示字段错误
    showFieldError(fieldId, message) {
        const field = DOMUtils.getElementById(fieldId);
        if (!field) return;

        DOMUtils.addClass(field, 'error');

        let errorElement = field.parentNode.querySelector('.error-message');
        if (!errorElement) {
            errorElement = DOMUtils.createElement('div', {
                className: 'error-message',
                textContent: message
            });
            field.parentNode.appendChild(errorElement);
        } else {
            DOMUtils.setText(errorElement, message);
        }
    }

    // 清除字段错误
    clearFieldError(fieldId) {
        const field = DOMUtils.getElementById(fieldId);
        if (!field) return;

        DOMUtils.removeClass(field, 'error');

        const errorElement = field.parentNode.querySelector('.error-message');
        if (errorElement) {
            errorElement.remove();
        }
    }

    // 显示验证错误
    showValidationErrors(errors) {
        const errorContainer = this.getOrCreateErrorContainer();
        DOMUtils.clearChildren(errorContainer);

        errors.forEach(error => {
            const errorItem = DOMUtils.createElement('div', {
                className: 'validation-error-item',
                textContent: error
            });
            errorContainer.appendChild(errorItem);
        });

        DOMUtils.show(errorContainer);
    }

    // 获取或创建错误容器
    getOrCreateErrorContainer() {
        let container = DOMUtils.getElementById('gameValidationErrors');
        if (!container) {
            container = DOMUtils.createElement('div', {
                id: 'gameValidationErrors',
                className: 'validation-errors hidden'
            });
            
            const form = document.querySelector('#gameMode .game-form');
            if (form) {
                form.appendChild(container);
            }
        }
        return container;
    }

    // 设置发送按钮状态
    setSendButtonState(loading) {
        const sendBtn = DOMUtils.getElementById('sendBtn');
        if (!sendBtn) return;

        if (loading) {
            sendBtn.disabled = true;
            sendBtn.textContent = '发送中...';
            DOMUtils.addClass(sendBtn, 'loading');
        } else {
            sendBtn.disabled = false;
            sendBtn.textContent = '发送';
            DOMUtils.removeClass(sendBtn, 'loading');
        }
    }

    // 重置表单
    resetForm() {
        const form = document.querySelector('#gameMode .game-form');
        if (form) {
            // 不完全重置，保留用户ID和成本类型
            const fieldsToReset = ['repeat', 'team', 'jy'];
            fieldsToReset.forEach(fieldId => {
                const field = DOMUtils.getElementById(fieldId);
                if (field) {
                    field.value = '';
                }
            });
        }

        // 清除所有错误
        const errorElements = form?.querySelectorAll('.error-message') || [];
        errorElements.forEach(element => element.remove());

        const errorFields = form?.querySelectorAll('.error') || [];
        errorFields.forEach(field => DOMUtils.removeClass(field, 'error'));

        // 隐藏验证错误容器
        const errorContainer = DOMUtils.getElementById('gameValidationErrors');
        if (errorContainer) {
            DOMUtils.hide(errorContainer);
        }

        // 重新初始化字段管理器
        if (this.fieldManager) {
            this.fieldManager.resetAllFields();
        }
    }

    // 获取表单数据
    getFormData() {
        return {
            userId: DOMUtils.getElementById('userId')?.value || this.currentUserId,
            messageType: DOMUtils.getElementById('apiType')?.value || '1',
            costType: DOMUtils.getElementById('costType')?.value || this.currentCostType,
            ...this.fieldManager.getCurrentFieldValues()
        };
    }

    // 设置表单数据
    setFormData(data) {
        if (data.userId) {
            const userIdInput = DOMUtils.getElementById('userId');
            if (userIdInput) {
                userIdInput.value = data.userId;
                this.currentUserId = parseInt(data.userId);
            }
        }

        if (data.messageType) {
            const apiTypeSelect = DOMUtils.getElementById('apiType');
            if (apiTypeSelect) {
                apiTypeSelect.value = data.messageType;
                this.fieldManager.handleInterfaceChange(data.messageType);
            }
        }

        if (data.costType) {
            const costTypeSelect = DOMUtils.getElementById('costType');
            if (costTypeSelect) {
                costTypeSelect.value = data.costType;
                this.currentCostType = parseInt(data.costType);
            }
        }

        // 设置字段值
        ['repeat', 'team', 'jy'].forEach(fieldName => {
            if (data[fieldName] !== undefined) {
                const field = DOMUtils.getElementById(fieldName);
                if (field) {
                    field.value = data[fieldName];
                }
            }
        });
    }
}