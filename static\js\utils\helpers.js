// 通用辅助函数
export class Helpers {
    // 格式化时间戳
    static formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    // 格式化文件大小
    static formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 深拷贝对象
    static deepClone(obj) {
        if (obj === null || typeof obj !== 'object') {
            return obj;
        }
        
        if (obj instanceof Date) {
            return new Date(obj.getTime());
        }
        
        if (obj instanceof Array) {
            return obj.map(item => this.deepClone(item));
        }
        
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    }

    // 生成唯一ID
    static generateId(prefix = 'id') {
        return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // 验证数字输入
    static validateNumber(value, min = null, max = null) {
        const num = parseFloat(value);
        
        if (isNaN(num)) {
            return { valid: false, error: '请输入有效数字' };
        }
        
        if (min !== null && num < min) {
            return { valid: false, error: `数值不能小于 ${min}` };
        }
        
        if (max !== null && num > max) {
            return { valid: false, error: `数值不能大于 ${max}` };
        }
        
        return { valid: true, value: num };
    }

    // 验证整数输入
    static validateInteger(value, min = null, max = null) {
        const validation = this.validateNumber(value, min, max);
        
        if (!validation.valid) {
            return validation;
        }
        
        if (!Number.isInteger(validation.value)) {
            return { valid: false, error: '请输入整数' };
        }
        
        return validation;
    }

    // 格式化JSON
    static formatJSON(obj, indent = 2) {
        try {
            return JSON.stringify(obj, null, indent);
        } catch (error) {
            return '无法格式化JSON: ' + error.message;
        }
    }

    // 解析JSON
    static parseJSON(str) {
        try {
            return { success: true, data: JSON.parse(str) };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // 延迟执行
    static delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 重试机制
    static async retry(fn, maxAttempts = 3, delayMs = 1000) {
        let lastError;
        
        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                return await fn();
            } catch (error) {
                lastError = error;
                console.warn(`尝试 ${attempt}/${maxAttempts} 失败:`, error.message);
                
                if (attempt < maxAttempts) {
                    await this.delay(delayMs);
                }
            }
        }
        
        throw lastError;
    }

    // 检查对象是否为空
    static isEmpty(obj) {
        if (obj == null) return true;
        if (Array.isArray(obj) || typeof obj === 'string') return obj.length === 0;
        if (typeof obj === 'object') return Object.keys(obj).length === 0;
        return false;
    }

    // 获取对象的嵌套属性
    static getNestedProperty(obj, path, defaultValue = undefined) {
        const keys = path.split('.');
        let current = obj;
        
        for (const key of keys) {
            if (current == null || typeof current !== 'object') {
                return defaultValue;
            }
            current = current[key];
        }
        
        return current !== undefined ? current : defaultValue;
    }

    // 设置对象的嵌套属性
    static setNestedProperty(obj, path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        let current = obj;
        
        for (const key of keys) {
            if (!(key in current) || typeof current[key] !== 'object') {
                current[key] = {};
            }
            current = current[key];
        }
        
        current[lastKey] = value;
    }

    // URL参数解析
    static parseURLParams(url = window.location.href) {
        const params = {};
        const urlObj = new URL(url);
        
        for (const [key, value] of urlObj.searchParams) {
            params[key] = value;
        }
        
        return params;
    }

    // 构建URL参数
    static buildURLParams(params) {
        const searchParams = new URLSearchParams();
        
        Object.entries(params).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
                searchParams.append(key, value);
            }
        });
        
        return searchParams.toString();
    }

    // 错误分类
    static classifyError(error) {
        if (error.name === 'TypeError') {
            return { category: 'type', severity: 'high' };
        } else if (error.name === 'ReferenceError') {
            return { category: 'reference', severity: 'high' };
        } else if (error.name === 'NetworkError' || error.message.includes('网络')) {
            return { category: 'network', severity: 'medium' };
        } else if (error.name === 'ValidationError') {
            return { category: 'validation', severity: 'low' };
        } else {
            return { category: 'unknown', severity: 'medium' };
        }
    }
}