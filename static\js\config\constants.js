// 系统常量配置
export const CONSTANTS = {
    // WebSocket 配置
    WS: {
        RECONNECT_INTERVAL: 3000,
        MAX_RECONNECT_ATTEMPTS: 5,
        HEARTBEAT_INTERVAL: 30000
    },

    // 接口类型
    INTERFACE_TYPES: {
        NORMAL_MONSTER: 1,
        SUPPLEMENT_POOL: 4,
        TREASURE_DRAW: 9,
        NEIDAN_EXPLORE: 11,
        ELIXIR_DROP: 20,
        E<PERSON><PERSON>IR_OPEN: 21,
        INTERFACE_22: 22
    },

    // 废弃接口列表 (但仍保留逻辑支持)
    DEPRECATED_INTERFACES: [2,3,5,6,7,8,10,12], // 只有接口2是完全废弃的

    // 消息类型
    MESSAGE_TYPES: {
        ADMIN_STATS_GET: 'admin_stats_get',
        ADMIN_STATS_SET: 'admin_stats_set',
        ADMIN_MODULE_INFO: 'admin_module_info',
        ADMIN_CLEANUP: 'admin_cleanup'
    },

    // 成本层级
    COST_TIERS: [100, 1000, 10000],

    // UI 状态
    UI_STATES: {
        CONNECTED: 'connected',
        DISCONNECTED: 'disconnected',
        CONNECTING: 'connecting'
    }
};

// 接口字段配置
export const INTERFACE_FIELD_CONFIGS = {
    1: { // 普通怪
        repeat: { visible: true, label: "连击", defaultValue: 1 },
        team: { visible: false, label: "队伍", defaultValue: 0 },
        jy: { visible: true, label: "精英", defaultValue: 0 }
    },
    3: { // 多人请求 (已废弃，但保留配置)
        repeat: { visible: true, label: "用户数量", defaultValue: 100 },
        team: {
            visible: true,
            label: "特殊队伍",
            options: [{ value: 0, text: "无" }, { value: 1, text: "S队" }],
            defaultValue: 0
        },
        jy: { visible: false, label: "精英", defaultValue: 0 }
    },
    4: { // 补充奖池
        repeat: { visible: true, label: "返还金额", defaultValue: 1000 },
        team: {
            visible: true,
            label: "模式",
            options: [{ value: 0, text: "全局" }, { value: 1, text: "个人" }],
            defaultValue: 0
        },
        jy: { visible: false, label: "精英", defaultValue: 0 }
    },
    5: { // 游戏局数请求 (已废弃)
        repeat: { visible: true, label: "游戏局数", defaultValue: 1 },
        team: { visible: false, label: "队伍", defaultValue: 0 },
        jy: { visible: false, label: "精英", defaultValue: 0 }
    },
    6: { // 地图请求 (已废弃)
        repeat: { visible: true, label: "倍数", defaultValue: 1 },
        team: { visible: true, label: "倍率", defaultValue: 1 },
        jy: { visible: false, label: "精英", defaultValue: 0 }
    },
    7: { // 排行榜请求 (已废弃)
        repeat: { visible: false, label: "连击", defaultValue: 1 },
        team: { visible: true, label: "排行榜ID", defaultValue: 1 },
        jy: { visible: false, label: "精英", defaultValue: 0 }
    },
    8: { // 排行榜请求 (已废弃)
        repeat: { visible: false, label: "连击", defaultValue: 1 },
        team: { visible: true, label: "排行榜ID", defaultValue: 1 },
        jy: { visible: false, label: "精英", defaultValue: 0 }
    },
    9: { // 宝物开奖
        repeat: { visible: false, label: "宝物数量", defaultValue: 1 },
        team: {
            visible: true,
            label: "开奖方式",
            options: [{ value: 0, text: "自动" }, { value: 1, text: "手动" }],
            defaultValue: 0
        },
        jy: { visible: false, label: "精英", defaultValue: 0 }
    },
    10: { // 已废弃，类似内丹探索
        repeat: { visible: true, label: "连击", defaultValue: 1 },
        team: { visible: false, label: "队伍", defaultValue: 0 },
        jy: { visible: true, label: "精英", defaultValue: 0 }
    },
    11: { // 内丹探索
        repeat: { visible: true, label: "连击", defaultValue: 1 },
        team: { visible: false, label: "队伍", defaultValue: 0 },
        jy: { visible: true, label: "精英", defaultValue: 0 }
    },
    12: { // 已废弃，类似宝物开奖
        repeat: { visible: true, label: "宝物数量", defaultValue: 1 },
        team: { visible: false, label: "队伍", defaultValue: 0 },
        jy: { visible: false, label: "精英", defaultValue: 0 }
    },
    20: { // 仙丹掉落
        repeat: { visible: true, label: "连击", defaultValue: 1 },
        team: { visible: false, label: "队伍", defaultValue: 0 },
        jy: { visible: true, label: "精英", defaultValue: 0 }
    },
    21: { // 仙丹开鼎
        repeat: { visible: true, label: "仙丹数量", defaultValue: 1 },
        team: { visible: false, label: "队伍", defaultValue: 0 },
        jy: { visible: false, label: "精英", defaultValue: 0 }
    },
    22: { // 接口22
        repeat: { visible: true, label: "连击", defaultValue: 1 },
        team: { visible: false, label: "队伍", defaultValue: 0 },
        jy: { visible: true, label: "精英", defaultValue: 0 }
    }
};