import { INTERFACE_FIELD_CONFIGS, CONSTANTS } from '../config/constants.js';
import { DOMUtils } from '../utils/dom.js';
import { Logger } from '../utils/logger.js';
import { Helpers } from '../utils/helpers.js';

// 接口字段管理核心类
export class InterfaceFieldManager {
    constructor(elements, options = {}) {
        this.elements = elements;
        this.onFieldChange = options.onFieldChange || (() => {});
        this.logger = options.logger || new Logger();
        
        this.fieldStates = {};
        this.currentInterface = null;

        // 验证DOM元素
        try {
            this.validateDOMElements();
        } catch (error) {
            this.logger.error('InterfaceFieldManager 初始化失败:', error);
            throw error;
        }

        this.initializeFieldStates();
        this.logger.info('InterfaceFieldManager 初始化成功');
    }

    // 验证DOM元素
    validateDOMElements() {
        const requiredElements = ['repeatEl', 'teamEl', 'jyEl'];
        const missing = [];

        requiredElements.forEach(elementName => {
            if (!this.elements[elementName]) {
                missing.push(elementName);
            }
        });

        if (missing.length > 0) {
            throw new Error(`缺少必需的DOM元素: ${missing.join(', ')}`);
        }
    }

    // 初始化字段状态存储
    initializeFieldStates() {
        // 为每个有效接口初始化默认状态
        Object.keys(INTERFACE_FIELD_CONFIGS).forEach(interfaceId => {
            const config = INTERFACE_FIELD_CONFIGS[interfaceId];
            this.fieldStates[interfaceId] = {};

            Object.keys(config).forEach(fieldName => {
                const fieldConfig = config[fieldName];
                if (fieldConfig.visible) {
                    this.fieldStates[interfaceId][fieldName] = fieldConfig.defaultValue;
                }
            });
        });

        this.logger.debug('字段状态存储初始化完成', this.fieldStates);
    }

    // 处理接口选择变化
    handleInterfaceChange(interfaceId) {
        try {
            let numericInterfaceId = parseInt(interfaceId);

            // 验证接口ID
            if (isNaN(numericInterfaceId)) {
                this.logger.error(`无效的接口ID: ${interfaceId}`);
                numericInterfaceId = 1; // 回退到默认接口
            }

            // 检查是否为完全废弃的接口
            if (CONSTANTS.DEPRECATED_INTERFACES.includes(numericInterfaceId)) {
                this.logger.warning(`接口 ${numericInterfaceId} 已废弃，自动切换到接口1`);
                if (this.elements.apiTypeEl) {
                    this.elements.apiTypeEl.value = "1";
                }
                numericInterfaceId = 1;
            }

            // 保存当前接口的字段状态
            if (this.currentInterface !== null) {
                this.saveCurrentFieldState(this.currentInterface);
            }

            // 更新当前接口
            const previousInterface = this.currentInterface;
            this.currentInterface = numericInterfaceId;

            // 获取新接口的配置
            const config = this.getInterfaceConfig(numericInterfaceId);

            // 更新字段显示和标签
            this.updateFieldVisibility(config);
            this.updateFieldLabels(config);

            // 恢复新接口的字段状态
            this.restoreFieldState(numericInterfaceId);

            // 特殊接口处理
            this.handleSpecialInterfaceLogic(numericInterfaceId);

            // 触发字段变化回调
            this.onFieldChange(numericInterfaceId, config);

            this.logger.info(`成功切换到接口 ${numericInterfaceId}`, {
                from: previousInterface,
                to: numericInterfaceId,
                config: config
            });

        } catch (error) {
            this.logger.error('处理接口变化时发生错误:', error);
            this.recoverToSafeState();
        }
    }

    // 获取接口配置
    getInterfaceConfig(interfaceId) {
        const config = INTERFACE_FIELD_CONFIGS[interfaceId];
        
        if (!config) {
            this.logger.warning(`接口 ${interfaceId} 的配置不存在，使用默认配置`);
            return this.getDefaultConfig();
        }

        return config;
    }

    // 获取默认配置
    getDefaultConfig() {
        return {
            repeat: { visible: true, label: "连击", defaultValue: 1 },
            team: { visible: true, label: "队伍", defaultValue: 0 },
            jy: { visible: true, label: "精英", defaultValue: 0 }
        };
    }

    // 更新字段可见性
    updateFieldVisibility(config) {
        Object.keys(config).forEach(fieldName => {
            const fieldConfig = config[fieldName];
            const fieldGroup = this.getFieldGroup(fieldName);

            if (fieldGroup) {
                if (fieldConfig.visible) {
                    DOMUtils.show(fieldGroup);
                } else {
                    DOMUtils.hide(fieldGroup);
                }
            }
        });
    }

    // 更新字段标签
    updateFieldLabels(config) {
        Object.keys(config).forEach(fieldName => {
            const fieldConfig = config[fieldName];
            if (fieldConfig.visible && fieldConfig.label) {
                const label = this.getFieldLabel(fieldName);
                if (label) {
                    DOMUtils.setText(label, fieldConfig.label + ':');
                }

                // 处理特殊字段选项（如接口9的手动开奖选项）
                if (fieldConfig.options && fieldName === 'team') {
                    this.updateFieldOptions(fieldName, fieldConfig.options);
                } else if (fieldName === 'team' && !fieldConfig.options) {
                    this.restoreFieldInput(fieldName);
                }

                // 设置字段默认值
                this.setFieldDefaultValue(fieldName, fieldConfig.defaultValue);
            }
        });
    }

    // 更新字段选项（将输入框转换为选择框）
    updateFieldOptions(fieldName, options) {
        const fieldElement = this.elements[fieldName + 'El'];
        if (fieldElement && fieldElement.tagName === 'INPUT') {
            const currentValue = fieldElement.value;
            const fieldGroup = this.getFieldGroup(fieldName);

            if (fieldGroup) {
                // 创建选择框
                const select = document.createElement('select');
                select.id = fieldElement.id;
                select.className = fieldElement.className;

                // 添加选项
                options.forEach(option => {
                    const optionElement = document.createElement('option');
                    optionElement.value = option.value;
                    optionElement.textContent = option.text;
                    select.appendChild(optionElement);
                });

                // 设置当前值
                const validValues = options.map(opt => opt.value.toString());
                if (validValues.includes(currentValue)) {
                    select.value = currentValue;
                } else {
                    select.value = options[0].value;
                }

                // 替换元素
                fieldElement.parentNode.replaceChild(select, fieldElement);
                this.elements[fieldName + 'El'] = select;

                // 特殊处理：接口9的手动开奖选项
                if (fieldName === 'team' && this.currentInterface === 9) {
                    this.handleInterface9TeamChange(select);
                }
            }
        }
    }

    // 恢复字段为普通输入框
    restoreFieldInput(fieldName) {
        const fieldElement = this.elements[fieldName + 'El'];
        if (fieldElement && fieldElement.tagName === 'SELECT') {
            const currentValue = fieldElement.value;

            // 创建输入框
            const input = document.createElement('input');
            input.type = 'number';
            input.id = fieldElement.id;
            input.className = fieldElement.className;
            input.value = currentValue;

            // 替换元素
            fieldElement.parentNode.replaceChild(input, fieldElement);
            this.elements[fieldName + 'El'] = input;
        }
    }

    // 处理特殊接口逻辑
    handleSpecialInterfaceLogic(interfaceId) {
        switch (interfaceId) {
            case 3:
                this.initializeInterface3Logic();
                break;
            case 4:
                this.initializeInterface4Logic();
                break;
            case 9:
                this.initializeInterface9Logic();
                break;
            default:
                // 其他接口无特殊逻辑
                break;
        }
    }

    // 初始化接口3的特殊逻辑 (多人请求)
    initializeInterface3Logic() {
        const teamElement = this.elements.teamEl;
        if (teamElement && teamElement.tagName === 'SELECT') {
            // 接口3的team字段控制是否有S队
            this.logger.debug('接口3: 多人请求模式');
        }
    }

    // 初始化接口4的特殊逻辑 (补充奖池)
    initializeInterface4Logic() {
        const teamElement = this.elements.teamEl;
        const repeatLabel = this.getFieldLabel('repeat');
        
        if (teamElement && teamElement.tagName === 'SELECT') {
            const handleChange = () => {
                const selectedValue = teamElement.value;
                if (selectedValue === '1') { // 个人模式
                    if (repeatLabel) {
                        DOMUtils.setText(repeatLabel, '返还金额:');
                    }
                    this.logger.debug('接口4: 个人补充奖池模式');
                } else { // 全局模式
                    if (repeatLabel) {
                        DOMUtils.setText(repeatLabel, '返还金额:');
                    }
                    this.logger.debug('接口4: 全局补充奖池模式');
                }
            };

            teamElement.addEventListener('change', handleChange);
            handleChange();
        }
    }

    // 初始化接口9的特殊逻辑
    initializeInterface9Logic() {
        const teamElement = this.elements.teamEl;
        if (teamElement && teamElement.tagName === 'SELECT') {
            this.handleInterface9TeamChange(teamElement);
        }
    }

    // 处理接口9的特殊逻辑：手动开奖选项变化
    handleInterface9TeamChange(selectElement) {
        const handleChange = () => {
            const selectedValue = selectElement.value;
            const repeatGroup = this.getFieldGroup('repeat');
            const repeatLabel = this.getFieldLabel('repeat');

            if (selectedValue === '1') { // 手动开奖
                if (repeatLabel) {
                    DOMUtils.setText(repeatLabel, '宝物数量:');
                }
                if (repeatGroup) {
                    DOMUtils.show(repeatGroup);
                }
                this.logger.debug('接口9: 手动开奖模式，显示宝物数量字段');
            } else { // 自动开奖
                if (repeatGroup) {
                    DOMUtils.hide(repeatGroup);
                }
                if (this.elements.repeatEl) {
                    this.elements.repeatEl.value = 1;
                }
                this.logger.debug('接口9: 自动开奖模式，隐藏宝物数量字段');
            }
        };

        // 绑定变化事件
        selectElement.addEventListener('change', handleChange);
        // 初始化状态
        handleChange();
    }

    // 设置字段默认值
    setFieldDefaultValue(fieldName, defaultValue) {
        try {
            const fieldElement = this.elements[fieldName + 'El'];
            if (fieldElement && defaultValue !== undefined) {
                const currentValue = fieldElement.value;
                
                if (!currentValue || currentValue === '' || currentValue === '0') {
                    fieldElement.value = defaultValue;
                    this.logger.debug(`设置字段 ${fieldName} 的默认值: ${defaultValue}`);
                }
            }
        } catch (error) {
            this.logger.error(`设置字段 ${fieldName} 默认值时发生错误:`, error);
        }
    }

    // 保存当前字段状态
    saveCurrentFieldState(interfaceId) {
        if (!this.fieldStates[interfaceId]) {
            this.fieldStates[interfaceId] = {};
        }

        ['repeat', 'team', 'jy'].forEach(fieldName => {
            const element = this.elements[fieldName + 'El'];
            if (element) {
                this.fieldStates[interfaceId][fieldName] = element.value;
            }
        });
    }

    // 恢复字段状态
    restoreFieldState(interfaceId) {
        const savedState = this.fieldStates[interfaceId];
        if (savedState) {
            Object.keys(savedState).forEach(fieldName => {
                const element = this.elements[fieldName + 'El'];
                if (element) {
                    element.value = savedState[fieldName];
                }
            });
        }
    }

    // 获取字段组
    getFieldGroup(fieldName) {
        const element = this.elements[fieldName + 'El'];
        return element ? element.closest('.form-group') : null;
    }

    // 获取字段标签
    getFieldLabel(fieldName) {
        const fieldGroup = this.getFieldGroup(fieldName);
        return fieldGroup ? fieldGroup.querySelector('label') : null;
    }

    // 恢复到安全状态
    recoverToSafeState() {
        this.logger.warning('开始恢复到安全状态...');
        
        try {
            // 重置到接口1
            this.currentInterface = 1;
            if (this.elements.apiTypeEl) {
                this.elements.apiTypeEl.value = "1";
            }

            // 显示所有字段
            ['repeat', 'team', 'jy'].forEach(fieldName => {
                const fieldGroup = this.getFieldGroup(fieldName);
                if (fieldGroup) {
                    DOMUtils.show(fieldGroup);
                }
            });

            // 重置字段值
            const defaultValues = { repeat: 1, team: 0, jy: 0 };
            Object.keys(defaultValues).forEach(fieldName => {
                const element = this.elements[fieldName + 'El'];
                if (element) {
                    element.value = defaultValues[fieldName];
                }
            });

            this.logger.info('成功恢复到安全状态');
        } catch (error) {
            this.logger.error('恢复到安全状态失败:', error);
        }
    }

    // 获取当前字段值
    getCurrentFieldValues() {
        const values = {};
        ['repeat', 'team', 'jy'].forEach(fieldName => {
            const element = this.elements[fieldName + 'El'];
            if (element) {
                values[fieldName] = element.value;
            }
        });
        return values;
    }

    // 验证字段值
    validateFieldValues() {
        const values = this.getCurrentFieldValues();
        const errors = [];

        // 验证repeat字段
        const repeatValidation = Helpers.validateInteger(values.repeat, 1, 100);
        if (!repeatValidation.valid) {
            errors.push(`连击数量: ${repeatValidation.error}`);
        }

        // 验证team字段
        const teamValidation = Helpers.validateInteger(values.team, 0, 10);
        if (!teamValidation.valid) {
            errors.push(`队伍: ${teamValidation.error}`);
        }

        // 验证jy字段
        const jyValidation = Helpers.validateInteger(values.jy, 0, 1);
        if (!jyValidation.valid) {
            errors.push(`精英: ${jyValidation.error}`);
        }

        return {
            valid: errors.length === 0,
            errors: errors,
            values: values
        };
    }

    // 重置所有字段
    resetAllFields() {
        this.fieldStates = {};
        this.initializeFieldStates();
        
        if (this.currentInterface) {
            this.restoreFieldState(this.currentInterface);
        }
        
        this.logger.info('所有字段已重置');
    }
}