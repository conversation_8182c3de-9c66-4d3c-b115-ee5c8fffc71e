import { CONSTANTS } from '../config/constants.js';
import { Logger } from '../utils/logger.js';
import { Helpers } from '../utils/helpers.js';

// API 请求处理类
export class APIManager {
    constructor(options = {}) {
        this.serverAddress = options.serverAddress || window.location.host;
        this.logger = options.logger || new Logger();
        this.baseURL = `http://${this.serverAddress}`;
    }

    // 通用HTTP请求方法
    async request(method, url, data = null, options = {}) {
        const config = {
            method: method.toUpperCase(),
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        if (data && ['POST', 'PUT', 'PATCH'].includes(config.method)) {
            config.body = JSON.stringify(data);
        }

        try {
            this.logger.info(`发送${method.toUpperCase()}请求到: ${url}`, data);
            
            const response = await fetch(url, config);
            const responseData = await this.handleResponse(response);
            
            this.logger.success(`请求成功: ${url}`, responseData);
            return responseData;
            
        } catch (error) {
            this.logger.error(`请求失败: ${url}`, error.message);
            throw error;
        }
    }

    // 处理响应
    async handleResponse(response) {
        const contentType = response.headers.get('content-type');
        
        let data;
        if (contentType && contentType.includes('application/json')) {
            data = await response.json();
        } else {
            data = await response.text();
        }

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${data.message || data}`);
        }

        return data;
    }

    // GET请求
    async get(endpoint, params = {}) {
        const url = new URL(endpoint, this.baseURL);
        Object.entries(params).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
                url.searchParams.append(key, value);
            }
        });
        
        return this.request('GET', url.toString());
    }

    // POST请求
    async post(endpoint, data = {}) {
        const url = new URL(endpoint, this.baseURL);
        return this.request('POST', url.toString(), data);
    }

    // PUT请求
    async put(endpoint, data = {}) {
        const url = new URL(endpoint, this.baseURL);
        return this.request('PUT', url.toString(), data);
    }

    // DELETE请求
    async delete(endpoint) {
        const url = new URL(endpoint, this.baseURL);
        return this.request('DELETE', url.toString());
    }

    // 管理员API - 获取统计数据
    async getStats(mapId) {
        return this.get(`/stats/global/${mapId}`);
    }

    // 管理员API - 设置统计数据
    async setStats(mapId, key, value) {
        return this.post(`/stats/global/${mapId}/${key}`, { value });
    }

    // 管理员API - 获取模块信息
    async getModuleInfo(mode = 'basic') {
        return this.get('/module', { mode });
    }

    // 管理员API - 数据清理
    async cleanup() {
        return this.post('/stats/cleanup');
    }

    // 日志API - 获取日志文件列表
    async getLogFiles() {
        return this.get('/api/logs/files');
    }

    // 日志API - 获取日志内容
    async getLogContent(filename, options = {}) {
        const params = {
            file: filename,
            lines: options.pageSize || 100,
            offset: options.offset || 0,
            ...options
        };
        return this.get('/api/logs/content', params);
    }

    // 日志API - 搜索日志
    async searchLogs(filename, keyword, options = {}) {
        const params = {
            file: filename,
            search: keyword,
            ...options
        };
        return this.get('/api/logs/content', params);
    }

    // 带重试的请求
    async requestWithRetry(method, endpoint, data = null, maxRetries = 3) {
        return Helpers.retry(
            () => this.request(method, endpoint, data),
            maxRetries,
            1000
        );
    }

    // 批量请求
    async batchRequest(requests) {
        const promises = requests.map(({ method, endpoint, data }) => 
            this.request(method, endpoint, data).catch(error => ({ error }))
        );
        
        return Promise.all(promises);
    }

    // 上传文件
    async uploadFile(endpoint, file, additionalData = {}) {
        const formData = new FormData();
        formData.append('file', file);
        
        Object.entries(additionalData).forEach(([key, value]) => {
            formData.append(key, value);
        });

        const config = {
            method: 'POST',
            body: formData
            // 不设置Content-Type，让浏览器自动设置
        };

        try {
            const url = new URL(endpoint, this.baseURL);
            const response = await fetch(url.toString(), config);
            return this.handleResponse(response);
        } catch (error) {
            this.logger.error(`文件上传失败: ${endpoint}`, error.message);
            throw error;
        }
    }

    // 下载文件
    async downloadFile(endpoint, filename) {
        try {
            const url = new URL(endpoint, this.baseURL);
            const response = await fetch(url.toString());
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            const blob = await response.blob();
            
            // 创建下载链接
            const downloadUrl = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(downloadUrl);
            
            this.logger.success(`文件下载成功: ${filename}`);
            
        } catch (error) {
            this.logger.error(`文件下载失败: ${endpoint}`, error.message);
            throw error;
        }
    }

    // 检查服务器状态
    async checkServerStatus() {
        try {
            const response = await fetch(`${this.baseURL}/health`, {
                method: 'GET',
                timeout: 5000
            });
            return response.ok;
        } catch (error) {
            return false;
        }
    }

    // 获取服务器信息
    async getServerInfo() {
        return this.get('/info');
    }
}