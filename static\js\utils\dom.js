// DOM 操作工具函数
export class DOMUtils {
    // 安全获取元素
    static getElementById(id) {
        const element = document.getElementById(id);
        if (!element) {
            console.warn(`元素未找到: ${id}`);
        }
        return element;
    }

    // 安全获取多个元素
    static getElementByIds(ids) {
        const elements = {};
        const missing = [];

        ids.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                elements[id] = element;
            } else {
                missing.push(id);
            }
        });

        if (missing.length > 0) {
            console.warn('以下元素未找到:', missing);
        }

        return { elements, missing };
    }

    // 显示元素
    static show(element) {
        if (element) {
            element.classList.remove('hidden');
            element.style.display = '';
        }
    }

    // 隐藏元素
    static hide(element) {
        if (element) {
            element.classList.add('hidden');
            // 同时设置内联样式，确保覆盖其他样式（避免某些情况下仍显示）
            element.style.display = 'none';
        }
    }

    // 切换元素显示状态
    static toggle(element) {
        if (element) {
            element.classList.toggle('hidden');
        }
    }

    // 设置元素文本内容
    static setText(element, text) {
        if (element) {
            element.textContent = text;
        }
    }

    // 设置元素HTML内容
    static setHTML(element, html) {
        if (element) {
            element.innerHTML = html;
        }
    }

    // 添加CSS类
    static addClass(element, className) {
        if (element && className) {
            element.classList.add(className);
        }
    }

    // 移除CSS类
    static removeClass(element, className) {
        if (element && className) {
            element.classList.remove(className);
        }
    }

    // 切换CSS类
    static toggleClass(element, className) {
        if (element && className) {
            element.classList.toggle(className);
        }
    }

    // 检查元素是否有指定类
    static hasClass(element, className) {
        return element && element.classList.contains(className);
    }

    // 清除元素的所有子节点
    static clearChildren(element) {
        if (element) {
            element.innerHTML = '';
        }
    }

    // 创建元素
    static createElement(tag, options = {}) {
        const element = document.createElement(tag);
        
        if (options.className) {
            element.className = options.className;
        }
        
        if (options.id) {
            element.id = options.id;
        }
        
        if (options.textContent) {
            element.textContent = options.textContent;
        }
        
        if (options.innerHTML) {
            element.innerHTML = options.innerHTML;
        }
        
        if (options.attributes) {
            Object.entries(options.attributes).forEach(([key, value]) => {
                element.setAttribute(key, value);
            });
        }
        
        if (options.colSpan) {
            element.colSpan = options.colSpan;
        }
        
        return element;
    }

    // 安全地执行DOM操作
    static safeExecute(operation, errorMessage = 'DOM操作失败', options = {}) {
        const { retries = 0, delay = 100 } = options;
        
        const execute = (attempt = 0) => {
            try {
                return operation();
            } catch (error) {
                console.error(`${errorMessage} (尝试 ${attempt + 1}):`, error);
                
                if (attempt < retries) {
                    setTimeout(() => execute(attempt + 1), delay);
                } else {
                    throw error;
                }
            }
        };
        
        return execute();
    }

    // 等待元素出现
    static waitForElement(selector, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const element = document.querySelector(selector);
            if (element) {
                resolve(element);
                return;
            }

            const observer = new MutationObserver((mutations, obs) => {
                const element = document.querySelector(selector);
                if (element) {
                    obs.disconnect();
                    resolve(element);
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            setTimeout(() => {
                observer.disconnect();
                reject(new Error(`元素 ${selector} 在 ${timeout}ms 内未出现`));
            }, timeout);
        });
    }

    // 防抖函数
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 节流函数
    static throttle(func, limit) {
        let inThrottle;
        return function executedFunction(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}