// Package logs provides log file reading and monitoring functionality
// for the jackpot web backend system.
//
// This package implements a concurrent-safe log reader service with
// memory monitoring and performance optimization for large files.
package logger

import (
	"fmt"
	"gopkg.in/ini.v1"
)

// Service represents the global log service instance
var Service *LogReaderService

// Initialize initializes the log service with the given configuration
func Initialize(cfg *ini.File) error {
	config, err := LoadConfigFromINI(cfg)
	if err != nil {
		return fmt.Errorf("failed to load log service configuration: %w", err)
	}
	
	Service = NewLogReaderServiceFromConfig(config)
	
	return nil
}

// GetService returns the global log service instance
func GetService() *LogReaderService {
	return Service
}

// IsInitialized returns true if the log service has been initialized
func IsInitialized() bool {
	return Service != nil
}