import { APIManager } from '../core/api.js';
import { DOMUtils } from '../utils/dom.js';
import { Logger } from '../utils/logger.js';
import { Helpers } from '../utils/helpers.js';

// 日志查看器组件类
export class LogViewerComponent {
    constructor(options = {}) {
        this.serverAddress = options.serverAddress || window.location.host;
        this.logger = options.logger || new Logger();
        this.apiManager = new APIManager({
            serverAddress: this.serverAddress,
            logger: this.logger
        });

        this.selectedFile = null;
        this.currentPage = 1;
        this.pageSize = 100;
        this.searchKeyword = '';
        this.autoRefresh = false;
        this.refreshInterval = null;

        // loading
        this._loadingCounter = 0;

        this.init();
    }

    // 初始化日志查看器
    init() {
        this.bindEvents();
        // 不在初始化时加载文件列表，等用户切换到日志视图时再加载
        this.logger.debug('日志查看器组件初始化完成');
    }

    // 绑定事件
    bindEvents() {
        // 文件选择事件
        const fileSelect = DOMUtils.getElementById('logFileSelect');
        if (fileSelect) {
            fileSelect.addEventListener('change', (e) => {
                this.selectFile(e.target.value);
            });
        }



        // 刷新按钮
        const refreshBtn = DOMUtils.getElementById('logRefreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshCurrentView();
            });
        }

        // 自动刷新切换
        const autoRefreshToggle = DOMUtils.getElementById('autoRefreshToggle');
        if (autoRefreshToggle) {
            autoRefreshToggle.addEventListener('change', (e) => {
                this.toggleAutoRefresh(e.target.checked);
            });
        }

        // 清空日志按钮
        const clearBtn = DOMUtils.getElementById('logClearBtn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearLogDisplay();
            });
        }

        // 下载日志按钮
        const downloadBtn = DOMUtils.getElementById('logDownloadBtn');
        if (downloadBtn) {
            downloadBtn.addEventListener('click', () => {
                this.downloadCurrentLog();
            });
        }

        // 分页按钮
        const prevBtn = DOMUtils.getElementById('logPrevBtn');
        const nextBtn = DOMUtils.getElementById('logNextBtn');
        
        if (prevBtn) {
            prevBtn.addEventListener('click', () => {
                this.previousPage();
            });
        }

        if (nextBtn) {
            nextBtn.addEventListener('click', () => {
                this.nextPage();
            });
        }
    }

    // 加载日志文件列表
    async loadLogFiles() {
        try {
            this.showLoading(true, '正在加载日志文件列表...');
            this.logger.debug('正在加载日志文件列表...');
            const files = await this.apiManager.getLogFiles();
            
            this.updateFileSelect(files);
            this.logger.result(`成功加载 ${files.length} 个日志文件`, true);
            
        } catch (error) {
            this.logger.result('加载日志文件列表失败', false);
            this.showError('无法加载日志文件列表');
        } finally {
            this.showLoading(false);
        }
    }

    // 更新文件选择下拉框
    updateFileSelect(response) {
        const fileSelect = DOMUtils.getElementById('logFileSelect');
        if (!fileSelect) return;

        // 清空现有选项
        DOMUtils.clearChildren(fileSelect);

        // 添加默认选项
        const defaultOption = DOMUtils.createElement('option', {
            textContent: '请选择日志文件',
            attributes: { value: '' }
        });
        fileSelect.appendChild(defaultOption);

        // 处理服务器响应格式
        const files = response.data || response;
        if (!Array.isArray(files)) {
            this.logger.warning('日志文件列表格式不正确');
            return;
        }

        // 添加文件选项
        files.forEach(file => {
            const option = DOMUtils.createElement('option', {
                textContent: `${file.name} (${file.size_human || Helpers.formatFileSize(file.size)})`,
                attributes: { value: file.name }
            });
            fileSelect.appendChild(option);
        });
    }

    // 选择文件
    async selectFile(filename) {
        if (!filename) {
            this.selectedFile = null;
            this.clearLogDisplay();
            // 停止自动刷新
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
                this.refreshInterval = null;
            }
            return;
        }

        this.selectedFile = filename;
        this.currentPage = 1;
        this.searchKeyword = '';

        // 如果自动刷新已勾选，启动自动刷新
        const autoRefreshToggle = DOMUtils.getElementById('autoRefreshToggle');
        if (autoRefreshToggle && autoRefreshToggle.checked && !this.refreshInterval) {
            this.refreshInterval = setInterval(() => {
                if (this.selectedFile) {
                    this.loadLogContent();
                }
            }, 5000);
            this.logger.system('自动刷新已启用');
        }

        await this.loadLogContent();
    }

    // 加载日志内容
    async loadLogContent() {
        if (!this.selectedFile) return;

        try {
            this.showLoading(true, `正在加载 ${this.selectedFile}...`);
            
            const offset = (this.currentPage - 1) * this.pageSize;
            const options = {
                pageSize: this.pageSize,
                offset: offset
            };

            const content = await this.apiManager.getLogContent(this.selectedFile, options);

            this.displayLogContent(content);
            this.updatePaginationInfo(content);
            
        } catch (error) {
            this.logger.error('加载日志内容失败:', error.message);
            this.showError('无法加载日志内容: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    // 显示日志内容
    displayLogContent(response) {
        const logDisplay = DOMUtils.getElementById('logDisplay');
        if (!logDisplay) return;

        DOMUtils.clearChildren(logDisplay);

        // 处理服务器响应格式
        const content = response.data || response;
        const logText = content.content || content;
        
        if (!logText || logText.trim() === '') {
            const noDataMsg = DOMUtils.createElement('div', {
                className: 'no-data-message',
                textContent: this.searchKeyword ? '没有找到匹配的日志' : '日志文件为空'
            });
            logDisplay.appendChild(noDataMsg);
            return;
        }

        // 将内容按行分割
        const lines = logText.split('\n');
        const startLine = content.start_line || 1;

        // 创建日志行
        lines.forEach((line, index) => {
            if (line.trim() !== '') { // 跳过空行
                const logLine = this.createLogLine(line, startLine + index);
                logDisplay.appendChild(logLine);
            }
        });

        // 滚动到顶部
        logDisplay.scrollTop = 0;
    }

    // 创建日志行元素
    createLogLine(line, lineNumber) {
        const logLine = DOMUtils.createElement('div', {
            className: 'log-line'
        });

        // 行号
        const lineNum = DOMUtils.createElement('span', {
            className: 'line-number',
            textContent: lineNumber
        });

        // 日志内容
        const content = DOMUtils.createElement('span', {
            className: 'line-content',
            textContent: line
        });

        // 根据日志级别添加样式
        const logLevel = this.detectLogLevel(line);
        if (logLevel) {
            DOMUtils.addClass(logLine, `log-level-${logLevel}`);
        }

        // 高亮搜索关键词
        if (this.searchKeyword) {
            this.highlightKeyword(content, this.searchKeyword);
        }

        logLine.appendChild(lineNum);
        logLine.appendChild(content);

        return logLine;
    }

    // 检测日志级别 - 增强版本，支持更多格式
    detectLogLevel(line) {
        const upperLine = line.toUpperCase();
        
        // 检测 level=ERROR, level=DEBUG 等格式
        if (upperLine.includes('LEVEL=ERROR') || upperLine.includes('ERROR') || upperLine.includes('错误')) {
            return 'error';
        } else if (upperLine.includes('LEVEL=WARN') || upperLine.includes('WARN') || upperLine.includes('WARNING') || upperLine.includes('警告')) {
            return 'warning';
        } else if (upperLine.includes('LEVEL=INFO') || upperLine.includes('INFO') || upperLine.includes('信息')) {
            return 'info';
        } else if (upperLine.includes('LEVEL=DEBUG') || upperLine.includes('DEBUG') || upperLine.includes('调试')) {
            return 'debug';
        }
        
        return null;
    }

    // 高亮关键词
    highlightKeyword(element, keyword) {
        const text = element.textContent;
        const regex = new RegExp(`(${keyword})`, 'gi');
        const highlightedText = text.replace(regex, '<mark>$1</mark>');
        element.innerHTML = highlightedText;
    }



    // 上一页
    async previousPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
            await this.loadLogContent();
        }
    }

    // 下一页
    async nextPage() {
        this.currentPage++;
        await this.loadLogContent();
    }

    // 更新分页信息
    updatePaginationInfo(response) {
        const pageInfo = DOMUtils.getElementById('logPageInfo');
        const content = response.data || response;
        
        if (pageInfo && content) {
            const totalLines = content.total_lines || 0;
            const startLine = content.start_line || 1;
            const endLine = content.end_line || startLine;
            
            DOMUtils.setText(pageInfo, `显示第 ${startLine}-${endLine} 行，共 ${totalLines} 行`);
        }

        // 更新分页按钮状态
        const prevBtn = DOMUtils.getElementById('logPrevBtn');
        const nextBtn = DOMUtils.getElementById('logNextBtn');
        
        if (prevBtn) {
            prevBtn.disabled = this.currentPage <= 1;
        }
        
        if (nextBtn) {
            // 简单的分页逻辑，如果返回的内容少于页面大小，说明没有更多内容了
            const lines = (content.content || '').split('\n').filter(line => line.trim() !== '');
            nextBtn.disabled = lines.length < this.pageSize;
        }
    }

    // 切换自动刷新
    toggleAutoRefresh(enabled) {
        this.autoRefresh = enabled;
        
        if (enabled) {
            // 只有在有选中文件时才启动自动刷新
            if (this.selectedFile) {
                this.refreshInterval = setInterval(() => {
                    if (this.selectedFile) {
                        this.loadLogContent();
                    }
                }, 5000); // 每5秒刷新一次
                
                this.logger.system('自动刷新已启用');
            } else {
                this.logger.warning('请先选择日志文件再启用自动刷新');
                // 取消勾选状态
                const autoRefreshToggle = DOMUtils.getElementById('autoRefreshToggle');
                if (autoRefreshToggle) {
                    autoRefreshToggle.checked = false;
                }
                this.autoRefresh = false;
            }
        } else {
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
                this.refreshInterval = null;
            }
            this.logger.system('自动刷新已禁用');
        }
    }

    // 刷新当前视图
    async refreshCurrentView() {
        if (this.selectedFile) {
            await this.loadLogContent();
            this.logger.debug('日志内容已刷新');
        } else {
            await this.loadLogFiles();
        }
    }

    // 清空日志显示
    clearLogDisplay() {
        const logDisplay = DOMUtils.getElementById('logDisplay');
        if (logDisplay) {
            DOMUtils.clearChildren(logDisplay);
        }

        const pageInfo = DOMUtils.getElementById('logPageInfo');
        if (pageInfo) {
            DOMUtils.setText(pageInfo, '');
        }
    }

    // 下载当前日志
    async downloadCurrentLog() {
        if (!this.selectedFile) {
            this.logger.warning('请先选择一个日志文件');
            return;
        }

        try {
            // 使用日志内容API来获取完整文件内容并下载
            const content = await this.apiManager.getLogContent(this.selectedFile, { full: true });
            
            // 创建下载
            const blob = new Blob([content.content || content], { type: 'text/plain;charset=utf-8' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = this.selectedFile;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
            
            this.logger.result(`日志文件 ${this.selectedFile} 下载成功`, true);
        } catch (error) {
            this.logger.error('下载日志文件失败:', error.message);
        }
    }

    // 显示加载状态
    showLoading(show, message = '加载中...') {
        const loadingIndicator = DOMUtils.getElementById('logLoadingIndicator');
        if (!loadingIndicator) return;

        if (show) {
            this._loadingCounter = Math.max(0, this._loadingCounter) + 1;
            loadingIndicator.textContent = message;
            DOMUtils.show(loadingIndicator);
        } else {
            // 计数器归零才真正隐藏，避免并发请求相互干扰
            this._loadingCounter = Math.max(0, this._loadingCounter - 1);
            if (this._loadingCounter === 0) {
                setTimeout(() => {
                    DOMUtils.hide(loadingIndicator);
                }, 100); // 小延迟避免闪烁
            }
        }
    }

    // 显示错误信息
    showError(message) {
        const logDisplay = DOMUtils.getElementById('logDisplay');
        if (logDisplay) {
            DOMUtils.clearChildren(logDisplay);
            
            const errorMsg = DOMUtils.createElement('div', {
                className: 'error-message',
                textContent: message
            });
            
            logDisplay.appendChild(errorMsg);
        }
    }

    // 导出日志（客户端处理）
    exportLogs(format = 'txt') {
        const logDisplay = DOMUtils.getElementById('logDisplay');
        if (!logDisplay) return;

        const lines = Array.from(logDisplay.querySelectorAll('.log-line'));
        let content = '';

        if (format === 'csv') {
            content = 'Line Number,Content\n';
            lines.forEach(line => {
                const lineNum = line.querySelector('.line-number')?.textContent || '';
                const lineContent = line.querySelector('.line-content')?.textContent || '';
                content += `"${lineNum}","${lineContent.replace(/"/g, '""')}"\n`;
            });
        } else {
            lines.forEach(line => {
                const lineNum = line.querySelector('.line-number')?.textContent || '';
                const lineContent = line.querySelector('.line-content')?.textContent || '';
                content += `[${lineNum}] ${lineContent}\n`;
            });
        }

        // 创建下载
        const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${this.selectedFile || 'log'}_export.${format}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        this.logger.result(`日志已导出为 ${format.toUpperCase()} 格式`, true);
    }

    // 销毁组件
    destroy() {
        // 停止自动刷新
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }

        this.logger.debug('日志查看器组件已销毁');
    }
}