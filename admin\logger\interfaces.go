package logger

import (
	"time"
)

// LogReader defines the interface for reading log files
type LogReader interface {
	// GetLogFiles returns a list of available log files
	GetLogFiles() ([]LogFile, error)
	
	// ReadLogContent reads log file content with line limits and offset
	ReadLogContent(filename string, lines int, offset int) (*LogContent, error)
	
	// TailLogFile reads the tail of a log file for real-time updates
	TailLogFile(filename string, lines int, since time.Time) (*LogTailData, error)
	
	// TailLogFileIncremental reads only new content added since the last read
	TailLogFileIncremental(filename string, lines int, lastSize int64, lastModified time.Time) (*LogTailData, error)
}

// FileWatcher defines the interface for monitoring file changes
type FileWatcher interface {
	// GetFileChangeStatus checks if a file has changed since the given parameters
	GetFileChangeStatus(filename string, lastSize int64, lastModified time.Time) (*FileChangeEvent, error)
	
	// StartFileWatcher starts monitoring a file and returns a channel for change events
	StartFileWatcher(filename string) (<-chan FileChangeEvent, error)
	
	// StopFileWatcher stops monitoring a file for a specific subscriber
	StopFileWatcher(filename string, subscriber <-chan FileChangeEvent) error
}

// PerformanceMonitor defines the interface for monitoring system performance
type PerformanceMonitor interface {
	// GetMetrics returns current performance metrics
	GetMetrics() *PerformanceMetrics
	
	// CheckMemoryUsage returns true if memory usage is within acceptable limits
	CheckMemoryUsage() bool
	
	// CanAcceptNewReader returns true if system can handle a new concurrent reader
	CanAcceptNewReader() bool
}

// FileValidator defines the interface for validating file access
type FileValidator interface {
	// ValidateFilename checks if the filename is valid and safe
	ValidateFilename(filename string) error
	
	// IsFileAccessible checks if the file can be accessed
	IsFileAccessible(filepath string) bool
	
	// GetSafeFilePath returns a safe file path within the logs directory
	GetSafeFilePath(filename string) (string, error)
}