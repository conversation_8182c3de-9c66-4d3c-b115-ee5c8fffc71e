import { WebSocketManager } from './core/websocket.js';
import { APIManager } from './core/api.js';
import { NavigationComponent } from './components/navigation.js';
import { LogViewerComponent } from './components/log-viewer.js';
import { ResultsPanel } from './components/results-panel.js';
import { AdminModule } from './modules/admin.js';
import { GameModule } from './modules/game.js';
import { Logger } from './utils/logger.js';
import { DOMUtils } from './utils/dom.js';
import { CONSTANTS } from './config/constants.js';

// 主应用类
class JackpotTestTool {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.serverAddress = window.location.host;

        // 初始化日志器
        this.logger = new Logger('output');

        // 初始化核心组件
        this.initializeComponents();

        // 初始化模块
        this.initializeModules();

        // 绑定事件
        this.bindEvents();

        // 初始化UI
        this.initializeUI();

        this.logger.system('奖池系统测试工具初始化完成');
    }

    // 初始化核心组件
    initializeComponents() {
        // WebSocket管理器
        this.wsManager = new WebSocketManager({
            serverAddress: this.serverAddress,
            logger: this.logger,
            onMessage: (data) => this.handleWebSocketMessage(data),
            onStatusChange: (status) => this.updateConnectionStatus(status)
        });

        // API管理器
        this.apiManager = new APIManager({
            serverAddress: this.serverAddress,
            logger: this.logger
        });

        // 导航组件
        this.navigationComponent = new NavigationComponent({
            logger: this.logger,
            onViewChange: (viewName) => this.handleViewChange(viewName)
        });

        // 日志查看器组件
        this.logViewerComponent = new LogViewerComponent({
            serverAddress: this.serverAddress,
            logger: this.logger
        });

        // 结果面板组件
        this.resultsPanel = new ResultsPanel();
    }

    // 初始化模块
    initializeModules() {
        // 管理员模块
        this.adminModule = new AdminModule({
            apiManager: this.apiManager,
            logger: this.logger,
            onResult: (result) => this.handleAdminResult(result)
        });

        // 游戏模块
        this.gameModule = new GameModule({
            wsManager: this.wsManager,
            logger: this.logger,
            onResult: (result) => this.handleGameResult(result)
        });
    }

    // 绑定事件
    bindEvents() {
        // 连接按钮
        const connectBtn = DOMUtils.getElementById('connectBtn');
        if (connectBtn) {
            connectBtn.addEventListener('click', () => {
                this.toggleConnection();
            });
        }



        // 模式切换按钮
        const adminModeBtn = DOMUtils.getElementById('adminModeBtn');
        const gameModeBtn = DOMUtils.getElementById('gameModeBtn');

        if (adminModeBtn) {
            adminModeBtn.addEventListener('click', () => {
                this.switchMode('admin');
            });
        }

        if (gameModeBtn) {
            gameModeBtn.addEventListener('click', () => {
                this.switchMode('game');
            });
        }

        // 清空输出按钮
        const clearBtn = DOMUtils.getElementById('clearBtn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.logger.clear();
                this.clearItemsTable();
                this.logger.system('已清空结果显示');
            });
        }

        // 详细日志切换
        const verboseLogging = DOMUtils.getElementById('verboseLogging');
        if (verboseLogging) {
            verboseLogging.addEventListener('change', (e) => {
                this.logger.setVerboseMode(e.target.checked);
            });
        }

        // 测试日志按钮
        const testLogBtn = DOMUtils.getElementById('testLogBtn');
        if (testLogBtn) {
            testLogBtn.addEventListener('click', () => {
                this.testLogs();
            });
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // 结果面板切换事件
        document.addEventListener('resultsPanelToggle', (e) => {
            this.handleResultsPanelToggle(e.detail);
        });

        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
    }

    // 初始化UI
    initializeUI() {
        // 设置初始连接状态
        this.updateConnectionStatus(CONSTANTS.UI_STATES.DISCONNECTED);

        // 设置默认模式
        this.switchMode('game');

        // 显示欢迎消息
        this.logger.system('欢迎使用奖池系统管理后台');
        this.logger.info(`服务器地址: ${this.serverAddress}`);

        // 添加调试信息
        this.logger.debug('所有组件初始化完成');
        this.logger.debug('WebSocket管理器状态:', this.wsManager.getStatus());
    }

    // 切换连接状态
    async toggleConnection() {
        if (this.isConnected) {
            await this.disconnect();
        } else {
            await this.connect();
        }
    }

    // 连接WebSocket
    async connect() {
        try {
            this.updateConnectionStatus(CONSTANTS.UI_STATES.CONNECTING);
            await this.wsManager.connect();
            this.isConnected = true;
        } catch (error) {
            this.logger.error('连接失败:', error.message);
            this.updateConnectionStatus(CONSTANTS.UI_STATES.DISCONNECTED);
        }
    }

    // 断开WebSocket连接
    async disconnect() {
        try {
            this.wsManager.disconnect();
            this.isConnected = false;
            this.updateConnectionStatus(CONSTANTS.UI_STATES.DISCONNECTED);
        } catch (error) {
            this.logger.error('断开连接失败:', error.message);
        }
    }

    // 更新连接状态显示
    updateConnectionStatus(status) {
        const statusElement = DOMUtils.getElementById('status');
        const connectBtn = DOMUtils.getElementById('connectBtn');

        if (!statusElement || !connectBtn) return;

        // 移除所有状态类
        DOMUtils.removeClass(statusElement, 'connected');
        DOMUtils.removeClass(statusElement, 'disconnected');
        DOMUtils.removeClass(statusElement, 'connecting');

        switch (status) {
            case CONSTANTS.UI_STATES.CONNECTED:
                DOMUtils.setText(statusElement, '已连接');
                DOMUtils.addClass(statusElement, 'connected');
                DOMUtils.setText(connectBtn, '断开');
                connectBtn.disabled = false;
                this.isConnected = true;
                break;

            case CONSTANTS.UI_STATES.CONNECTING:
                DOMUtils.setText(statusElement, '连接中...');
                DOMUtils.addClass(statusElement, 'connecting');
                DOMUtils.setText(connectBtn, '连接中...');
                connectBtn.disabled = true;
                break;

            case CONSTANTS.UI_STATES.DISCONNECTED:
            default:
                DOMUtils.setText(statusElement, '未连接');
                DOMUtils.addClass(statusElement, 'disconnected');
                DOMUtils.setText(connectBtn, '连接');
                connectBtn.disabled = false;
                this.isConnected = false;
                break;
        }
    }

    // 切换模式
    switchMode(mode) {
        const adminMode = DOMUtils.getElementById('adminMode');
        const gameMode = DOMUtils.getElementById('gameMode');
        const adminModeBtn = DOMUtils.getElementById('adminModeBtn');
        const gameModeBtn = DOMUtils.getElementById('gameModeBtn');

        if (!adminMode || !gameMode || !adminModeBtn || !gameModeBtn) return;

        // 移除所有活跃状态
        DOMUtils.removeClass(adminModeBtn, 'active');
        DOMUtils.removeClass(gameModeBtn, 'active');
        DOMUtils.addClass(adminMode, 'hidden');
        DOMUtils.addClass(gameMode, 'hidden');

        // 激活选中的模式
        if (mode === 'admin') {
            DOMUtils.addClass(adminModeBtn, 'active');
            DOMUtils.removeClass(adminMode, 'hidden');
            this.logger.system('已切换到管理员模式');
        } else {
            DOMUtils.addClass(gameModeBtn, 'active');
            DOMUtils.removeClass(gameMode, 'hidden');
            this.logger.system('已切换到游戏接口模式');
        }
    }

    // 处理视图变化
    handleViewChange(viewName) {
        this.logger.debug(`视图切换到: ${viewName}`);

        // 根据视图执行特定的初始化逻辑
        switch (viewName) {
            case 'log-viewer':
                this.logger.system('已切换到日志查看器');
                // 日志查看器视图激活时加载日志文件
                if (this.logViewerComponent) {
                    this.logViewerComponent.loadLogFiles().catch(error => {
                        this.logger.warning('无法加载服务器日志文件，请检查服务器配置');
                    });
                }
                break;
            case 'api-test':
                this.logger.system('已切换到接口测试');
                break;
        }
    }

    // 处理WebSocket消息
    handleWebSocketMessage(data) {
        // 检查数据有效性
        if (!data) {
            this.logger.warning('收到空的WebSocket消息');
            return;
        }
        
        // 根据消息类型进行处理
        if (data.header !== undefined) {
            // 二进制消息
            this.handleBinaryMessage(data);
        } else if (typeof data === 'object' && data.text) {
            // 文本消息
            this.handleTextMessage(data);
        } else {
            // 其他类型消息
            this.handleTextMessage(data);
        }
    }

    // 处理二进制消息
    handleBinaryMessage(data) {
        // 检查数据有效性
        if (!data) {
            this.logger.error('收到空的二进制消息数据');
            return;
        }

        const { header, data: messageData, error, isHeartbeat } = data;

        // 如果是心跳消息，直接返回
        if (isHeartbeat) {
            return;
        }

        // 如果解析时出现错误
        if (error) {
            this.logger.error(`消息解析错误: ${error}`);
            return;
        }

        // 检查消息头
        if (!header) {
            this.logger.error('消息头缺失');
            return;
        }

        // 显示游戏响应结果
        const messageType = header.messageType || header.msgType;
        
        // 特殊处理心跳消息和其他系统消息
        if (messageType === 0 || header.msgState === 6) {
            // 心跳消息，不需要显示
            this.logger.debug('收到心跳响应');
            return;
        }
        
        if (messageType === undefined || messageType === null) {
            this.logger.debug('收到系统消息，消息状态:', header.msgState);
            return;
        }
        
        if (messageData && messageData.code === 0) {
            this.logger.result(`接口${messageType}请求成功`, true);
        } else if (messageData && messageData.code !== undefined) {
            this.logger.result(`接口${messageType}请求失败: ${messageData.msg || '未知错误'}`, false);
        } else if (messageData) {
            this.logger.warning(`接口${messageType}响应格式异常，收到数据:`, messageData);
        } else {
            this.logger.debug(`接口${messageType}收到空响应（可能是心跳或系统消息）`);
        }

        // 详细信息仅在详细模式下显示
        this.logger.debug('完整响应数据:', {
            messageType: messageType,
            userId: header.userId ? header.userId.toString() : 'unknown',
            data: messageData
        });

        // 更新道具列表 - 检查多种可能的数据结构
        let items = null;
        if (messageData) {
            // 根据test/main.go的响应结构，道具数据在data.items中
            if (messageData.data && messageData.data.items) {
                items = messageData.data.items;
            } else if (messageData.items) {
                items = messageData.items;
            } else if (Array.isArray(messageData)) {
                items = messageData;
            }

            if (items && Array.isArray(items) && items.length > 0) {
                this.updateItemsTable(items);
                this.logger.info(`获得 ${items.length} 种道具`);
            } else {
                this.logger.debug('响应中未找到道具数据');
            }
        } else {
            this.logger.debug('收到空的消息数据');
        }
    }

    // 处理文本消息
    handleTextMessage(data) {
        if (data.type === 'ping') {
            // 心跳响应
            return;
        }

        this.logger.info('服务器消息:', data);
    }

    // 处理管理员操作结果
    handleAdminResult(result) {
        if (result.error) {
            this.logger.result(`管理员操作失败: ${result.error}`, false);
        } else {
            this.logger.result('管理员操作成功', true);
            this.logger.debug('操作详情:', result);
        }
    }

    // 处理游戏操作结果
    handleGameResult(result) {
        if (result.error) {
            this.logger.result(`游戏操作失败: ${result.error}`, false);
        } else if (result.type === 'quickTest') {
            const { success, failed, total } = result.summary;
            this.logger.result(`快速测试完成: 成功${success}次，失败${failed}次，共${total}次`, success > failed);
            
            // 详细结果仅在详细模式下显示
            result.results.forEach((r, i) => {
                if (r.error) {
                    this.logger.debug(`测试 ${i + 1} 失败: ${r.error}`);
                } else {
                    this.logger.debug(`测试 ${i + 1} 成功`);
                }
            });
        } else if (result.type === 'batchTest') {
            const { success, failed, total } = result.summary;
            this.logger.result(`批量测试完成: 成功${success}次，失败${failed}次，共${total}次`, success > failed);
            
            // 详细结果仅在详细模式下显示
            result.results.forEach((r, i) => {
                if (r.error) {
                    this.logger.debug(`批量测试 ${i + 1} 失败: ${r.error}`);
                } else {
                    this.logger.debug(`批量测试 ${i + 1} 成功`);
                }
            });
        } else {
            this.logger.result('游戏操作成功', true);
            this.logger.debug('操作详情:', result);
        }
    }

    // 处理结果面板切换
    handleResultsPanelToggle(detail) {
        const { isCollapsed, height } = detail;
        this.logger.debug(`结果面板${isCollapsed ? '收缩' : '展开'}: 高度 ${height}px`);

        // 可以在这里添加其他需要响应面板状态变化的逻辑
        // 例如调整其他UI元素的位置或大小
    }

    // 处理键盘快捷键
    handleKeyboardShortcuts(e) {
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case 'Enter':
                    e.preventDefault();
                    // 根据当前模式执行相应操作
                    const currentMode = this.getCurrentMode();
                    if (currentMode === 'admin') {
                        const submitBtn = DOMUtils.getElementById('adminSubmit');
                        if (submitBtn && !submitBtn.disabled) {
                            submitBtn.click();
                        }
                    } else {
                        const sendBtn = DOMUtils.getElementById('sendBtn');
                        if (sendBtn && !sendBtn.disabled) {
                            sendBtn.click();
                        }
                    }
                    break;

                case 'l':
                    e.preventDefault();
                    this.logger.clear();
                    break;
            }
        }
    }

    // 获取当前模式
    getCurrentMode() {
        const adminModeBtn = DOMUtils.getElementById('adminModeBtn');
        if (adminModeBtn && DOMUtils.hasClass(adminModeBtn, 'active')) {
            return 'admin';
        }
        return 'game';
    }

    // 测试日志功能
    testLogs() {
        const logTypes = [
            { type: 'info', message: '这是一条信息日志' },
            { type: 'success', message: '操作成功完成' },
            { type: 'warning', message: '这是一条警告信息' },
            { type: 'error', message: '这是一条错误信息' },
            { type: 'debug', message: '这是一条调试信息（需要开启详细日志才能看到）' }
        ];

        logTypes.forEach((log, index) => {
            setTimeout(() => {
                if (log.type === 'success') {
                    this.logger.result(log.message, true);
                } else if (log.type === 'error') {
                    this.logger.result(log.message, false);
                } else {
                    this.logger[log.type](log.message);
                }
            }, index * 500);
        });

        this.logger.system('开始测试各种类型的日志...');
    }

    // 清理资源
    cleanup() {
        if (this.wsManager) {
            this.wsManager.disconnect();
        }

        if (this.logViewerComponent) {
            this.logViewerComponent.destroy();
        }

        if (this.navigationComponent) {
            this.navigationComponent.destroy();
        }

        if (this.resultsPanel) {
            this.resultsPanel.destroy();
        }

        this.logger.debug('应用资源已清理');
    }

    // 获取应用状态
    getAppState() {
        return {
            connected: this.isConnected,
            currentMode: this.getCurrentMode(),
            currentView: this.navigationComponent.getCurrentView(),
            serverAddress: this.serverAddress
        };
    }



    // 更新道具列表
    updateItemsTable(items) {
        const tableBody = DOMUtils.getElementById('itemsTableBody');

        if (!tableBody) {
            this.logger.error('未找到itemsTableBody元素');
            return;
        }

        // 清空现有内容
        DOMUtils.clearChildren(tableBody);

        if (!items || items.length === 0) {
            const noDataRow = DOMUtils.createElement('tr', {
                className: 'no-data'
            });
            const noDataCell = DOMUtils.createElement('td', {
                textContent: '暂无数据',
                colSpan: 5
            });
            noDataRow.appendChild(noDataCell);
            tableBody.appendChild(noDataRow);
            return;
        }

        // 添加道具数据
        items.forEach((item) => {
            const row = DOMUtils.createElement('tr');

            // 名称
            const nameCell = DOMUtils.createElement('td', {
                textContent: item.name || '未知道具'
            });

            // 单价/价值
            const valueCell = DOMUtils.createElement('td', {
                textContent: item.value || 0
            });

            // 次数/数量 (count字段)
            const countCell = DOMUtils.createElement('td', {
                textContent: item.count || 0
            });

            // 总量 (total字段)
            const totalQuantityCell = DOMUtils.createElement('td', {
                textContent: item.total || 0
            });

            // 总值 (基于total计算)
            const totalValue = (item.value || 0) * (item.total || 0);
            const totalValueCell = DOMUtils.createElement('td', {
                textContent: totalValue
            });

            row.appendChild(nameCell);
            row.appendChild(valueCell);
            row.appendChild(countCell);
            row.appendChild(totalQuantityCell);
            row.appendChild(totalValueCell);

            tableBody.appendChild(row);
        });

        this.logger.debug(`道具列表已更新，共 ${items.length} 个道具`);
    }

    // 清空道具列表
    clearItemsTable() {
        const tableBody = DOMUtils.getElementById('itemsTableBody');
        if (!tableBody) return;

        DOMUtils.clearChildren(tableBody);

        const noDataRow = DOMUtils.createElement('tr', {
            className: 'no-data'
        });
        const noDataCell = DOMUtils.createElement('td', {
            textContent: '暂无数据',
            colSpan: 5
        });
        noDataRow.appendChild(noDataCell);
        tableBody.appendChild(noDataRow);

        this.logger.debug('道具列表已清空');
    }



    // 导出日志
    exportLogs() {
        const logs = this.logger.exportLogs();
        const blob = new Blob([logs], { type: 'text/plain;charset=utf-8' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `jackpot_logs_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        this.logger.result('日志已导出', true);
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.jackpotApp = new JackpotTestTool();
    } catch (error) {
        console.error('应用初始化失败:', error);
        alert('应用初始化失败，请刷新页面重试');
    }
});

// 导出应用类供调试使用
export { JackpotTestTool };