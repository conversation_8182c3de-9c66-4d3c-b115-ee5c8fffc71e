// 结果面板组件 - 处理展开/收缩功能
import { DOMUtils } from '../utils/dom.js';

export class ResultsPanel {
    constructor() {
        this.isCollapsed = false;
        this.footer = null;
        this.toggleBtn = null;
        this.storageKey = 'resultsPanel_collapsed';
        
        this.init();
    }

    init() {
        // 获取DOM元素
        this.footer = DOMUtils.getElementById('resultsFooter');
        this.toggleBtn = DOMUtils.getElementById('toggleResultsBtn');
        this.resizeHandle = DOMUtils.getElementById('resizeHandle');

        if (!this.footer || !this.toggleBtn) {
            console.warn('结果面板元素未找到');
            return;
        }

        // 初始化拖拽相关变量
        this.isDragging = false;
        this.startY = 0;
        this.startHeight = 0;
        this.currentHeight = 400; // 默认高度

        // 绑定事件
        this.bindEvents();

        // 恢复上次的状态
        this.restoreState();
    }

    bindEvents() {
        // 切换按钮点击事件
        this.toggleBtn.addEventListener('click', () => {
            this.toggle();
        });

        // 键盘快捷键支持 (Ctrl+R 或 Cmd+R)
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 'r' && !e.shiftKey) {
                e.preventDefault();
                this.toggle();
            }
        });

        // 双击标题栏切换
        const header = this.footer.querySelector('.results-header h3');
        if (header) {
            header.addEventListener('dblclick', () => {
                this.toggle();
            });
            header.style.cursor = 'pointer';
            header.title = '双击展开/收缩';
        }

        // 监听Logger的智能展开事件
        document.addEventListener('loggerSmartExpand', (e) => {
            this.smartToggle(e.detail.hasContent);
        });

        // 拖拽调整高度事件
        if (this.resizeHandle) {
            this.resizeHandle.addEventListener('mousedown', (e) => {
                this.startResize(e);
            });
        }

        // 全局鼠标事件
        document.addEventListener('mousemove', (e) => {
            this.handleResize(e);
        });

        document.addEventListener('mouseup', () => {
            this.endResize();
        });
    }

    toggle() {
        this.isCollapsed = !this.isCollapsed;
        this.updateUI();
        this.saveState();
        
        // 触发自定义事件
        this.dispatchToggleEvent();
    }

    expand() {
        if (this.isCollapsed) {
            this.isCollapsed = false;
            this.updateUI();
            this.saveState();
            this.dispatchToggleEvent();
        }
    }

    collapse() {
        if (!this.isCollapsed) {
            this.isCollapsed = true;
            this.updateUI();
            this.saveState();
            this.dispatchToggleEvent();
        }
    }

    updateUI() {
        if (!this.footer || !this.toggleBtn) return;

        const toggleText = this.toggleBtn.querySelector('.toggle-text');
        const mainContent = document.querySelector('.main-content');

        if (this.isCollapsed) {
            DOMUtils.addClass(this.footer, 'collapsed');
            this.toggleBtn.title = '展开结果面板';
            this.footer.style.height = '50px';
            if (toggleText) toggleText.textContent = '展开';

            // 动态调整主内容区域的底部边距
            if (mainContent) {
                mainContent.style.marginBottom = '60px';
            }
        } else {
            DOMUtils.removeClass(this.footer, 'collapsed');
            this.toggleBtn.title = '收缩结果面板';
            this.footer.style.height = `${this.currentHeight}px`;
            if (toggleText) toggleText.textContent = '收缩';

            // 恢复主内容区域的底部边距
            if (mainContent) {
                mainContent.style.marginBottom = `${this.currentHeight + 20}px`;
            }
        }

        // 更新图标方向在CSS中通过类控制
    }

    saveState() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.isCollapsed));
        } catch (error) {
            console.warn('无法保存结果面板状态:', error);
        }
    }

    restoreState() {
        try {
            const saved = localStorage.getItem(this.storageKey);
            if (saved !== null) {
                this.isCollapsed = JSON.parse(saved);
                this.updateUI();
            }
        } catch (error) {
            console.warn('无法恢复结果面板状态:', error);
        }

        // 恢复高度设置
        this.restoreHeight();
    }

    dispatchToggleEvent() {
        const event = new CustomEvent('resultsPanelToggle', {
            detail: {
                isCollapsed: this.isCollapsed,
                height: this.isCollapsed ? 50 : 400
            }
        });
        document.dispatchEvent(event);
    }

    // 获取当前状态
    getState() {
        return {
            isCollapsed: this.isCollapsed,
            height: this.isCollapsed ? 50 : 400
        };
    }

    // 设置状态（不触发事件）
    setState(collapsed) {
        this.isCollapsed = collapsed;
        this.updateUI();
        this.saveState();
    }

    // 添加动画完成回调
    onAnimationComplete(callback) {
        if (!this.footer) return;

        const handleTransitionEnd = (e) => {
            if (e.target === this.footer && e.propertyName === 'height') {
                callback(this.getState());
                this.footer.removeEventListener('transitionend', handleTransitionEnd);
            }
        };

        this.footer.addEventListener('transitionend', handleTransitionEnd);
    }

    // 智能切换：根据内容自动展开
    smartToggle(hasContent = false) {
        if (hasContent && this.isCollapsed) {
            this.expand();
        }
    }

    // 开始拖拽调整
    startResize(e) {
        if (this.isCollapsed) return; // 收缩状态下不允许调整

        this.isDragging = true;
        this.startY = e.clientY;
        this.startHeight = this.footer.offsetHeight;

        // 禁用过渡动画
        this.footer.style.transition = 'none';

        // 添加拖拽样式
        document.body.style.cursor = 'ns-resize';
        document.body.style.userSelect = 'none';

        e.preventDefault();
    }

    // 处理拖拽
    handleResize(e) {
        if (!this.isDragging) return;

        const deltaY = this.startY - e.clientY; // 向上拖拽为正值
        const newHeight = Math.max(50, Math.min(window.innerHeight * 0.8, this.startHeight + deltaY));

        this.footer.style.height = `${newHeight}px`;
        this.currentHeight = newHeight;
    }

    // 结束拖拽
    endResize() {
        if (!this.isDragging) return;

        this.isDragging = false;

        // 恢复过渡动画
        this.footer.style.transition = 'height 0.3s ease-in-out';

        // 恢复样式
        document.body.style.cursor = '';
        document.body.style.userSelect = '';

        // 更新主内容区域的边距
        const mainContent = document.querySelector('.main-content');
        if (mainContent && !this.isCollapsed) {
            mainContent.style.marginBottom = `${this.currentHeight + 20}px`;
        }

        // 保存新高度
        this.saveHeight();

        // 触发高度变化事件
        this.dispatchHeightChangeEvent();
    }

    // 保存高度设置
    saveHeight() {
        try {
            localStorage.setItem('resultsPanel_height', this.currentHeight.toString());
        } catch (error) {
            console.warn('无法保存结果面板高度:', error);
        }
    }

    // 恢复高度设置
    restoreHeight() {
        try {
            const saved = localStorage.getItem('resultsPanel_height');
            if (saved) {
                this.currentHeight = parseInt(saved, 10);
                if (!this.isCollapsed) {
                    this.footer.style.height = `${this.currentHeight}px`;
                }
            }

            // 初始化时设置主内容区域的边距
            const mainContent = document.querySelector('.main-content');
            if (mainContent) {
                if (this.isCollapsed) {
                    mainContent.style.marginBottom = '60px';
                } else {
                    mainContent.style.marginBottom = `${this.currentHeight + 20}px`;
                }
            }
        } catch (error) {
            console.warn('无法恢复结果面板高度:', error);
        }
    }

    // 触发高度变化事件
    dispatchHeightChangeEvent() {
        const event = new CustomEvent('resultsPanelHeightChange', {
            detail: {
                height: this.currentHeight,
                isCollapsed: this.isCollapsed
            }
        });
        document.dispatchEvent(event);
    }

    // 销毁组件
    destroy() {
        if (this.toggleBtn) {
            this.toggleBtn.removeEventListener('click', this.toggle);
        }
        
        document.removeEventListener('keydown', this.handleKeydown);
        
        const header = this.footer?.querySelector('.results-header h3');
        if (header) {
            header.removeEventListener('dblclick', this.toggle);
        }
    }
}
