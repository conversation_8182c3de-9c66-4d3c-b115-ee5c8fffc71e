package logger

import (
	"fmt"
	"gopkg.in/ini.v1"
)

// Config holds configuration for the log service
type Config struct {
	LogDirectory       string
	MaxConcurrentReaders int32
	MemoryThresholdMB   int64
	MaxLinesPerRequest  int
	AllowedExtensions   []string
	EnableAuditLog     bool
	EnablePerfLog      bool
}

// DefaultConfig returns a default configuration
func DefaultConfig() *Config {
	return &Config{
		LogDirectory:       "./logs",
		MaxConcurrentReaders: 3,
		MemoryThresholdMB:   512,
		MaxLinesPerRequest:  1000,
		AllowedExtensions:   []string{".log"},
		EnableAuditLog:     false, // 默认关闭审计日志
		EnablePerfLog:      false, // 默认关闭性能日志
	}
}

// LoadConfigFromINI loads configuration from an INI file
func LoadConfigFromINI(cfg *ini.File) (*Config, error) {
	config := DefaultConfig()
	
	if cfg.HasSection("log_viewer") {
		section := cfg.Section("log_viewer")
		
		if section.HasKey("log_directory") {
			config.LogDirectory = section.Key("log_directory").String()
		}
		
		if section.HasKey("max_concurrent_readers") {
			maxReaders, err := section.Key("max_concurrent_readers").Int()
			if err != nil {
				return nil, fmt.Errorf("invalid max_concurrent_readers value: %w", err)
			}
			config.MaxConcurrentReaders = int32(maxReaders)
		}
		
		if section.HasKey("memory_threshold_mb") {
			threshold, err := section.Key("memory_threshold_mb").Int64()
			if err != nil {
				return nil, fmt.Errorf("invalid memory_threshold_mb value: %w", err)
			}
			config.MemoryThresholdMB = threshold
		}
		
		if section.HasKey("max_lines_per_request") {
			maxLines, err := section.Key("max_lines_per_request").Int()
			if err != nil {
				return nil, fmt.Errorf("invalid max_lines_per_request value: %w", err)
			}
			config.MaxLinesPerRequest = maxLines
		}
		
		if section.HasKey("enable_audit_log") {
			config.EnableAuditLog, _ = section.Key("enable_audit_log").Bool()
		}
		
		if section.HasKey("enable_perf_log") {
			config.EnablePerfLog, _ = section.Key("enable_perf_log").Bool()
		}
	}
	
	return config, nil
}

// NewLogReaderServiceFromConfig creates a new LogReaderService from configuration
func NewLogReaderServiceFromConfig(config *Config) *LogReaderService {
	service := NewLogReaderServiceWithConfig(
		config.LogDirectory,
		config.MaxConcurrentReaders,
		config.MemoryThresholdMB,
		config.EnableAuditLog,
		config.EnablePerfLog,
	)
	
	service.maxLinesPerRequest = config.MaxLinesPerRequest
	service.allowedExtensions = config.AllowedExtensions
	
	return service
}