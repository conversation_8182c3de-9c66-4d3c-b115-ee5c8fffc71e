package logger

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

// LogReaderService implements the LogReader interface with concurrency control and memory monitoring
type LogReaderService struct {
	logDir            string
	maxConcurrent     int32
	currentReaders    int32
	memoryThreshold   int64 // Memory threshold in bytes
	maxLinesPerRequest int
	allowedExtensions []string
	
	// Mutex for thread-safe operations
	readerMutex sync.RWMutex
	
	// Performance metrics
	metrics *PerformanceMetrics
	metricsMutex sync.RWMutex
	
	// Request tracking
	requestCount int64
	totalReadTime int64
	errorCount   int64
	rejectedRequests int64
	
	// Rate limiting
	rateLimiter *RateLimiter
	
	// File monitoring
	fileWatcher *FileWatcherService
	
	// Performance logging
	perfLogger *log.Logger
	
	// Security and audit logging
	auditLogger *log.Logger
	securityLogger *log.Logger
}

// FileWatcherService provides file change monitoring capabilities
type FileWatcherService struct {
	watchers map[string]*FileWatcherImpl
	mutex    sync.RWMutex
}

// FileWatcherImpl monitors a specific file for changes
type FileWatcherImpl struct {
	filename     string
	lastSize     int64
	lastModified time.Time
	subscribers  []chan FileChangeEvent
	mutex        sync.RWMutex
}

// RateLimiter implements token bucket rate limiting
type RateLimiter struct {
	maxRequests   int32
	windowSize    time.Duration
	requests      []time.Time
	mutex         sync.Mutex
}

// RequestMetrics tracks detailed request metrics
type RequestMetrics struct {
	Timestamp    time.Time
	Operation    string
	Duration     time.Duration
	Success      bool
	ErrorMessage string
	MemoryUsage  int64
	ActiveReaders int32
}



// NewLogReaderService creates a new LogReaderService instance with default settings (audit and perf logs disabled)
func NewLogReaderService(logDir string, maxConcurrent int32, memoryThresholdMB int64) *LogReaderService {
	return NewLogReaderServiceWithConfig(logDir, maxConcurrent, memoryThresholdMB, false, false)
}

// NewLogReaderServiceWithConfig creates a new LogReaderService instance with configurable logging
func NewLogReaderServiceWithConfig(logDir string, maxConcurrent int32, memoryThresholdMB int64, enableAuditLog, enablePerfLog bool) *LogReaderService {
	// Create performance logger - configurable output
	var perfLogger *log.Logger
	if enablePerfLog {
		perfLogger = log.New(os.Stdout, "[LOG-PERF] ", log.LstdFlags|log.Lmicroseconds)
	} else {
		perfLogger = log.New(io.Discard, "[LOG-PERF] ", log.LstdFlags|log.Lmicroseconds)
	}
	
	// Create audit logger for access tracking - configurable output
	var auditLogger *log.Logger
	if enableAuditLog {
		auditLogger = log.New(os.Stdout, "[LOG-AUDIT] ", log.LstdFlags|log.Lmicroseconds)
	} else {
		auditLogger = log.New(io.Discard, "[LOG-AUDIT] ", log.LstdFlags|log.Lmicroseconds)
	}
	
	// Create security logger for security events (always enabled for security reasons)
	securityLogger := log.New(os.Stdout, "[LOG-SECURITY] ", log.LstdFlags|log.Lmicroseconds)
	
	service := &LogReaderService{
		logDir:            logDir,
		maxConcurrent:     maxConcurrent,
		currentReaders:    0,
		memoryThreshold:   memoryThresholdMB * 1024 * 1024, // Convert MB to bytes
		maxLinesPerRequest: 1000,
		allowedExtensions: []string{".log"},
		metrics: &PerformanceMetrics{
			ActiveReaders:   0,
			MemoryUsage:     0,
			RequestCount:    0,
			AverageReadTime: 0,
			LastUpdateTime:  time.Now(),
		},
		rateLimiter: NewRateLimiter(5, time.Second), // 5 requests per second
		fileWatcher: NewFileWatcherService(),
		perfLogger:  perfLogger,
		auditLogger: auditLogger,
		securityLogger: securityLogger,
	}
	
	// Log service initialization
	service.perfLogger.Printf("LogReaderService initialized - MaxConcurrent: %d, MemoryThreshold: %dMB, RateLimit: 5req/s", 
		maxConcurrent, memoryThresholdMB)
	service.auditLogger.Printf("LogReaderService initialized with security features enabled")
	
	return service
}

// NewFileWatcherService creates a new FileWatcherService instance
func NewFileWatcherService() *FileWatcherService {
	return &FileWatcherService{
		watchers: make(map[string]*FileWatcherImpl),
	}
}

// NewRateLimiter creates a new rate limiter with specified max requests per window
func NewRateLimiter(maxRequests int32, windowSize time.Duration) *RateLimiter {
	return &RateLimiter{
		maxRequests: maxRequests,
		windowSize:  windowSize,
		requests:    make([]time.Time, 0),
	}
}

// Allow checks if a request is allowed under the current rate limit
func (rl *RateLimiter) Allow() bool {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()
	
	now := time.Now()
	
	// Remove requests outside the current window
	cutoff := now.Add(-rl.windowSize)
	validRequests := make([]time.Time, 0)
	for _, reqTime := range rl.requests {
		if reqTime.After(cutoff) {
			validRequests = append(validRequests, reqTime)
		}
	}
	rl.requests = validRequests
	
	// Check if we can accept a new request
	if int32(len(rl.requests)) >= rl.maxRequests {
		return false
	}
	
	// Add current request
	rl.requests = append(rl.requests, now)
	return true
}

// GetCurrentRate returns the current request rate
func (rl *RateLimiter) GetCurrentRate() float64 {
	rl.mutex.Lock()
	defer rl.mutex.Unlock()
	
	now := time.Now()
	cutoff := now.Add(-rl.windowSize)
	
	validCount := 0
	for _, reqTime := range rl.requests {
		if reqTime.After(cutoff) {
			validCount++
		}
	}
	
	return float64(validCount) / rl.windowSize.Seconds()
}

// GetLogFiles returns a list of available log files
func (s *LogReaderService) GetLogFiles() ([]LogFile, error) {
	return s.GetLogFilesWithClient("")
}

// GetLogFilesWithClient returns a list of available log files with client IP logging
func (s *LogReaderService) GetLogFilesWithClient(clientIP string) ([]LogFile, error) {
	startTime := time.Now()
	operation := "GetLogFiles"
	
	// Log access attempt
	s.logAccessAttempt(operation, "directory_listing", clientIP, true, "")
	
	// Check rate limiting first
	if !s.rateLimiter.Allow() {
		atomic.AddInt64(&s.rejectedRequests, 1)
		s.logAccessAttempt(operation, "directory_listing", clientIP, false, "rate limit exceeded")
		s.logRequestMetrics(RequestMetrics{
			Timestamp:    startTime,
			Operation:    operation,
			Duration:     time.Since(startTime),
			Success:      false,
			ErrorMessage: "rate limit exceeded",
			MemoryUsage:  s.getCurrentMemoryUsage(),
			ActiveReaders: atomic.LoadInt32(&s.currentReaders),
		})
		return nil, fmt.Errorf("rate limit exceeded, please try again later")
	}
	
	if !s.CanAcceptNewReader() {
		atomic.AddInt64(&s.rejectedRequests, 1)
		s.logAccessAttempt(operation, "directory_listing", clientIP, false, "system at capacity")
		s.logRequestMetrics(RequestMetrics{
			Timestamp:    startTime,
			Operation:    operation,
			Duration:     time.Since(startTime),
			Success:      false,
			ErrorMessage: "system at capacity",
			MemoryUsage:  s.getCurrentMemoryUsage(),
			ActiveReaders: atomic.LoadInt32(&s.currentReaders),
		})
		return nil, fmt.Errorf("system is at capacity, cannot process new requests")
	}
	
	s.incrementReaders()
	defer s.decrementReaders()
	
	defer func() {
		duration := time.Since(startTime)
		s.updateMetrics(duration)
		s.logRequestMetrics(RequestMetrics{
			Timestamp:    startTime,
			Operation:    operation,
			Duration:     duration,
			Success:      true,
			MemoryUsage:  s.getCurrentMemoryUsage(),
			ActiveReaders: atomic.LoadInt32(&s.currentReaders),
		})
	}()
	
	files, err := os.ReadDir(s.logDir)
	if err != nil {
		atomic.AddInt64(&s.errorCount, 1)
		return nil, fmt.Errorf("failed to read log directory: %w", err)
	}
	
	var logFiles []LogFile
	for _, file := range files {
		if file.IsDir() {
			continue
		}
		
		// Check if file has allowed extension
		if !s.hasAllowedExtension(file.Name()) {
			continue
		}
		
		info, err := file.Info()
		if err != nil {
			continue
		}
		
		logFile := LogFile{
			Name:         file.Name(),
			Size:         info.Size(),
			ModifiedTime: info.ModTime(),
			SizeHuman:    formatFileSize(info.Size()),
		}
		
		logFiles = append(logFiles, logFile)
	}
	
	return logFiles, nil
}

// ReadLogContent reads log file content with line limits and offset
func (s *LogReaderService) ReadLogContent(filename string, lines int, offset int) (*LogContent, error) {
	return s.ReadLogContentWithClient(filename, lines, offset, "")
}

// ReadLogContentWithClient reads log file content with client IP logging
func (s *LogReaderService) ReadLogContentWithClient(filename string, lines int, offset int, clientIP string) (*LogContent, error) {
	startTime := time.Now()
	operation := fmt.Sprintf("ReadLogContent(%s)", filename)
	
	// Log access attempt
	s.logAccessAttempt(operation, filename, clientIP, true, "")
	
	// Check rate limiting first
	if !s.rateLimiter.Allow() {
		atomic.AddInt64(&s.rejectedRequests, 1)
		s.logAccessAttempt(operation, filename, clientIP, false, "rate limit exceeded")
		s.logRequestMetrics(RequestMetrics{
			Timestamp:    startTime,
			Operation:    operation,
			Duration:     time.Since(startTime),
			Success:      false,
			ErrorMessage: "rate limit exceeded",
			MemoryUsage:  s.getCurrentMemoryUsage(),
			ActiveReaders: atomic.LoadInt32(&s.currentReaders),
		})
		return nil, fmt.Errorf("rate limit exceeded, please try again later")
	}
	
	if !s.CanAcceptNewReader() {
		atomic.AddInt64(&s.rejectedRequests, 1)
		s.logAccessAttempt(operation, filename, clientIP, false, "system at capacity")
		s.logRequestMetrics(RequestMetrics{
			Timestamp:    startTime,
			Operation:    operation,
			Duration:     time.Since(startTime),
			Success:      false,
			ErrorMessage: "system at capacity",
			MemoryUsage:  s.getCurrentMemoryUsage(),
			ActiveReaders: atomic.LoadInt32(&s.currentReaders),
		})
		return nil, fmt.Errorf("system is at capacity, cannot process new requests")
	}
	
	if err := s.ValidateFilename(filename); err != nil {
		atomic.AddInt64(&s.errorCount, 1)
		s.logAccessAttempt(operation, filename, clientIP, false, err.Error())
		s.logRequestMetrics(RequestMetrics{
			Timestamp:    startTime,
			Operation:    operation,
			Duration:     time.Since(startTime),
			Success:      false,
			ErrorMessage: err.Error(),
			MemoryUsage:  s.getCurrentMemoryUsage(),
			ActiveReaders: atomic.LoadInt32(&s.currentReaders),
		})
		return nil, err
	}
	
	if lines > s.maxLinesPerRequest {
		atomic.AddInt64(&s.errorCount, 1)
		err := fmt.Errorf("requested lines (%d) exceeds maximum allowed (%d)", lines, s.maxLinesPerRequest)
		s.logAccessAttempt(operation, filename, clientIP, false, err.Error())
		s.logSecurityEvent("EXCESSIVE_LINES_REQUEST", filename, clientIP, fmt.Sprintf("Requested %d lines, max allowed %d", lines, s.maxLinesPerRequest))
		s.logRequestMetrics(RequestMetrics{
			Timestamp:    startTime,
			Operation:    operation,
			Duration:     time.Since(startTime),
			Success:      false,
			ErrorMessage: err.Error(),
			MemoryUsage:  s.getCurrentMemoryUsage(),
			ActiveReaders: atomic.LoadInt32(&s.currentReaders),
		})
		return nil, err
	}
	
	s.incrementReaders()
	defer s.decrementReaders()
	
	defer func() {
		duration := time.Since(startTime)
		s.updateMetrics(duration)
		s.logRequestMetrics(RequestMetrics{
			Timestamp:    startTime,
			Operation:    operation,
			Duration:     duration,
			Success:      true,
			MemoryUsage:  s.getCurrentMemoryUsage(),
			ActiveReaders: atomic.LoadInt32(&s.currentReaders),
		})
	}()
	
	filePath, err := s.GetSafeFilePath(filename)
	if err != nil {
		atomic.AddInt64(&s.errorCount, 1)
		return nil, err
	}
	
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		atomic.AddInt64(&s.errorCount, 1)
		return nil, fmt.Errorf("failed to get file info: %w", err)
	}
	
	// For large files (>10MB), use tail reading strategy
	if fileInfo.Size() > 10*1024*1024 {
		return s.readLargeFile(filePath, filename, lines, offset, fileInfo)
	}
	
	return s.readSmallFile(filePath, filename, lines, offset, fileInfo)
}

// TailLogFile reads the tail of a log file for real-time updates with enhanced monitoring
func (s *LogReaderService) TailLogFile(filename string, lines int, since time.Time) (*LogTailData, error) {
	return s.TailLogFileWithClient(filename, lines, since, "")
}

// TailLogFileWithClient reads the tail of a log file with client IP logging
func (s *LogReaderService) TailLogFileWithClient(filename string, lines int, since time.Time, clientIP string) (*LogTailData, error) {
	startTime := time.Now()
	operation := fmt.Sprintf("TailLogFile(%s)", filename)
	
	// Log access attempt
	s.logAccessAttempt(operation, filename, clientIP, true, "")
	
	// Check rate limiting first
	if !s.rateLimiter.Allow() {
		atomic.AddInt64(&s.rejectedRequests, 1)
		s.logAccessAttempt(operation, filename, clientIP, false, "rate limit exceeded")
		s.logRequestMetrics(RequestMetrics{
			Timestamp:    startTime,
			Operation:    operation,
			Duration:     time.Since(startTime),
			Success:      false,
			ErrorMessage: "rate limit exceeded",
			MemoryUsage:  s.getCurrentMemoryUsage(),
			ActiveReaders: atomic.LoadInt32(&s.currentReaders),
		})
		return nil, fmt.Errorf("rate limit exceeded, please try again later")
	}
	
	if !s.CanAcceptNewReader() {
		atomic.AddInt64(&s.rejectedRequests, 1)
		s.logAccessAttempt(operation, filename, clientIP, false, "system at capacity")
		s.logRequestMetrics(RequestMetrics{
			Timestamp:    startTime,
			Operation:    operation,
			Duration:     time.Since(startTime),
			Success:      false,
			ErrorMessage: "system at capacity",
			MemoryUsage:  s.getCurrentMemoryUsage(),
			ActiveReaders: atomic.LoadInt32(&s.currentReaders),
		})
		return nil, fmt.Errorf("system is at capacity, cannot process new requests")
	}
	
	if err := s.ValidateFilename(filename); err != nil {
		atomic.AddInt64(&s.errorCount, 1)
		s.logAccessAttempt(operation, filename, clientIP, false, err.Error())
		s.logRequestMetrics(RequestMetrics{
			Timestamp:    startTime,
			Operation:    operation,
			Duration:     time.Since(startTime),
			Success:      false,
			ErrorMessage: err.Error(),
			MemoryUsage:  s.getCurrentMemoryUsage(),
			ActiveReaders: atomic.LoadInt32(&s.currentReaders),
		})
		return nil, err
	}
	
	s.incrementReaders()
	defer s.decrementReaders()
	
	defer func() {
		duration := time.Since(startTime)
		s.updateMetrics(duration)
		s.logRequestMetrics(RequestMetrics{
			Timestamp:    startTime,
			Operation:    operation,
			Duration:     duration,
			Success:      true,
			MemoryUsage:  s.getCurrentMemoryUsage(),
			ActiveReaders: atomic.LoadInt32(&s.currentReaders),
		})
	}()
	
	filePath, err := s.GetSafeFilePath(filename)
	if err != nil {
		atomic.AddInt64(&s.errorCount, 1)
		return nil, err
	}
	
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		atomic.AddInt64(&s.errorCount, 1)
		return nil, fmt.Errorf("failed to get file info: %w", err)
	}
	
	// Enhanced file change detection: check both modification time and size
	hasNewData := fileInfo.ModTime().After(since)
	
	tailData := &LogTailData{
		Filename:     filename,
		NewContent:   "",
		HasNewData:   hasNewData,
		CurrentSize:  fileInfo.Size(),
		LastModified: fileInfo.ModTime(),
	}
	
	if hasNewData {
		// Read the tail of the file with enhanced algorithm
		content, err := s.readFileTailEnhanced(filePath, lines)
		if err != nil {
			atomic.AddInt64(&s.errorCount, 1)
			return nil, err
		}
		tailData.NewContent = content
	}
	
	return tailData, nil
}

// TailLogFileIncremental reads only new content added since the last read
func (s *LogReaderService) TailLogFileIncremental(filename string, lines int, lastSize int64, lastModified time.Time) (*LogTailData, error) {
	return s.TailLogFileIncrementalWithClient(filename, lines, lastSize, lastModified, "")
}

// TailLogFileIncrementalWithClient reads only new content added since the last read with client IP logging
func (s *LogReaderService) TailLogFileIncrementalWithClient(filename string, lines int, lastSize int64, lastModified time.Time, clientIP string) (*LogTailData, error) {
	startTime := time.Now()
	operation := fmt.Sprintf("TailLogFileIncremental(%s)", filename)
	
	// Log access attempt
	s.logAccessAttempt(operation, filename, clientIP, true, "")
	
	// Check rate limiting first
	if !s.rateLimiter.Allow() {
		atomic.AddInt64(&s.rejectedRequests, 1)
		s.logAccessAttempt(operation, filename, clientIP, false, "rate limit exceeded")
		s.logRequestMetrics(RequestMetrics{
			Timestamp:    startTime,
			Operation:    operation,
			Duration:     time.Since(startTime),
			Success:      false,
			ErrorMessage: "rate limit exceeded",
			MemoryUsage:  s.getCurrentMemoryUsage(),
			ActiveReaders: atomic.LoadInt32(&s.currentReaders),
		})
		return nil, fmt.Errorf("rate limit exceeded, please try again later")
	}
	
	if !s.CanAcceptNewReader() {
		atomic.AddInt64(&s.rejectedRequests, 1)
		s.logAccessAttempt(operation, filename, clientIP, false, "system at capacity")
		s.logRequestMetrics(RequestMetrics{
			Timestamp:    startTime,
			Operation:    operation,
			Duration:     time.Since(startTime),
			Success:      false,
			ErrorMessage: "system at capacity",
			MemoryUsage:  s.getCurrentMemoryUsage(),
			ActiveReaders: atomic.LoadInt32(&s.currentReaders),
		})
		return nil, fmt.Errorf("system is at capacity, cannot process new requests")
	}
	
	if err := s.ValidateFilename(filename); err != nil {
		atomic.AddInt64(&s.errorCount, 1)
		s.logAccessAttempt(operation, filename, clientIP, false, err.Error())
		s.logRequestMetrics(RequestMetrics{
			Timestamp:    startTime,
			Operation:    operation,
			Duration:     time.Since(startTime),
			Success:      false,
			ErrorMessage: err.Error(),
			MemoryUsage:  s.getCurrentMemoryUsage(),
			ActiveReaders: atomic.LoadInt32(&s.currentReaders),
		})
		return nil, err
	}
	
	s.incrementReaders()
	defer s.decrementReaders()
	
	defer func() {
		duration := time.Since(startTime)
		s.updateMetrics(duration)
		s.logRequestMetrics(RequestMetrics{
			Timestamp:    startTime,
			Operation:    operation,
			Duration:     duration,
			Success:      true,
			MemoryUsage:  s.getCurrentMemoryUsage(),
			ActiveReaders: atomic.LoadInt32(&s.currentReaders),
		})
	}()
	
	filePath, err := s.GetSafeFilePath(filename)
	if err != nil {
		atomic.AddInt64(&s.errorCount, 1)
		return nil, err
	}
	
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		atomic.AddInt64(&s.errorCount, 1)
		return nil, fmt.Errorf("failed to get file info: %w", err)
	}
	
	currentSize := fileInfo.Size()
	currentModTime := fileInfo.ModTime()
	
	// Check if file has new content (primarily based on size increase)
	// For log files, size increase is the most reliable indicator of new content
	hasNewData := currentSize > lastSize
	
	tailData := &LogTailData{
		Filename:     filename,
		NewContent:   "",
		HasNewData:   hasNewData,
		CurrentSize:  currentSize,
		LastModified: currentModTime,
	}
	
	if hasNewData {
		// Read only the new content added since last read
		newContent, err := s.readIncrementalContent(filePath, lastSize, currentSize)
		if err != nil {
			atomic.AddInt64(&s.errorCount, 1)
			return nil, err
		}
		
		// Apply line limit to new content
		if lines > 0 {
			newContent = s.limitLines(newContent, lines)
		}
		
		tailData.NewContent = newContent
	}
	
	return tailData, nil
}

// GetMetrics returns current performance metrics
func (s *LogReaderService) GetMetrics() *PerformanceMetrics {
	s.metricsMutex.RLock()
	defer s.metricsMutex.RUnlock()
	
	// Update current metrics
	s.metrics.ActiveReaders = atomic.LoadInt32(&s.currentReaders)
	s.metrics.MemoryUsage = s.getCurrentMemoryUsage()
	s.metrics.RequestCount = atomic.LoadInt64(&s.requestCount)
	
	if s.metrics.RequestCount > 0 {
		s.metrics.AverageReadTime = float64(atomic.LoadInt64(&s.totalReadTime)) / float64(s.metrics.RequestCount) / float64(time.Millisecond)
	}
	
	return s.metrics
}

// GetDetailedMetrics returns comprehensive performance metrics including rate limiting and error stats
func (s *LogReaderService) GetDetailedMetrics() map[string]interface{} {
	s.metricsMutex.RLock()
	defer s.metricsMutex.RUnlock()
	
	currentMemory := s.getCurrentMemoryUsage()
	requestCount := atomic.LoadInt64(&s.requestCount)
	errorCount := atomic.LoadInt64(&s.errorCount)
	rejectedRequests := atomic.LoadInt64(&s.rejectedRequests)
	
	var avgReadTime float64
	if requestCount > 0 {
		avgReadTime = float64(atomic.LoadInt64(&s.totalReadTime)) / float64(requestCount) / float64(time.Millisecond)
	}
	
	var errorRate float64
	if requestCount > 0 {
		errorRate = float64(errorCount) / float64(requestCount) * 100
	}
	
	var rejectionRate float64
	totalAttempts := requestCount + rejectedRequests
	if totalAttempts > 0 {
		rejectionRate = float64(rejectedRequests) / float64(totalAttempts) * 100
	}
	
	return map[string]interface{}{
		"activeReaders":       atomic.LoadInt32(&s.currentReaders),
		"maxConcurrent":       s.maxConcurrent,
		"memoryUsage":         currentMemory,
		"memoryUsageHuman":    formatFileSize(currentMemory),
		"memoryThreshold":     s.memoryThreshold,
		"memoryThresholdHuman": formatFileSize(s.memoryThreshold),
		"memoryUtilization":   float64(currentMemory) / float64(s.memoryThreshold) * 100,
		"requestCount":        requestCount,
		"errorCount":          errorCount,
		"rejectedRequests":    rejectedRequests,
		"averageReadTime":     avgReadTime,
		"errorRate":           errorRate,
		"rejectionRate":       rejectionRate,
		"currentRate":         s.rateLimiter.GetCurrentRate(),
		"maxRate":             float64(s.rateLimiter.maxRequests),
		"lastUpdateTime":      s.metrics.LastUpdateTime,
		"uptime":              time.Since(s.metrics.LastUpdateTime),
	}
}

// logRequestMetrics logs detailed request metrics for monitoring
func (s *LogReaderService) logRequestMetrics(metrics RequestMetrics) {
	if metrics.Success {
		s.perfLogger.Printf("SUCCESS %s - Duration: %.2fms, Memory: %s, ActiveReaders: %d", 
			metrics.Operation, 
			float64(metrics.Duration.Nanoseconds())/1e6,
			formatFileSize(metrics.MemoryUsage),
			metrics.ActiveReaders)
	} else {
		s.perfLogger.Printf("ERROR %s - Duration: %.2fms, Error: %s, Memory: %s, ActiveReaders: %d", 
			metrics.Operation, 
			float64(metrics.Duration.Nanoseconds())/1e6,
			metrics.ErrorMessage,
			formatFileSize(metrics.MemoryUsage),
			metrics.ActiveReaders)
	}
	
	// Log performance warnings
	if metrics.Duration > 3*time.Second {
		s.perfLogger.Printf("SLOW_REQUEST %s - Duration: %.2fs exceeded 3s threshold", 
			metrics.Operation, metrics.Duration.Seconds())
	}
	
	if metrics.MemoryUsage > s.memoryThreshold*8/10 { // 80% of threshold
		s.perfLogger.Printf("HIGH_MEMORY %s - Memory usage: %s (%.1f%% of threshold)", 
			metrics.Operation, 
			formatFileSize(metrics.MemoryUsage),
			float64(metrics.MemoryUsage)/float64(s.memoryThreshold)*100)
	}
	
	if metrics.ActiveReaders >= s.maxConcurrent*8/10 { // 80% of max concurrent
		s.perfLogger.Printf("HIGH_CONCURRENCY %s - Active readers: %d (%.1f%% of max)", 
			metrics.Operation, 
			metrics.ActiveReaders,
			float64(metrics.ActiveReaders)/float64(s.maxConcurrent)*100)
	}
}

// CheckMemoryUsage returns true if memory usage is within acceptable limits
func (s *LogReaderService) CheckMemoryUsage() bool {
	currentMemory := s.getCurrentMemoryUsage()
	return currentMemory < s.memoryThreshold
}

// CanAcceptNewReader returns true if system can handle a new concurrent reader
func (s *LogReaderService) CanAcceptNewReader() bool {
	currentReaders := atomic.LoadInt32(&s.currentReaders)
	return currentReaders < s.maxConcurrent && s.CheckMemoryUsage()
}

// ValidateFilename checks if the filename is valid and safe with enhanced security checks
func (s *LogReaderService) ValidateFilename(filename string) error {
	if filename == "" {
		s.securityLogger.Printf("SECURITY_VIOLATION: Empty filename provided")
		return fmt.Errorf("filename cannot be empty")
	}
	
	// Enhanced path traversal detection
	if s.containsPathTraversal(filename) {
		s.securityLogger.Printf("SECURITY_VIOLATION: Path traversal attempt detected in filename: %s", filename)
		return fmt.Errorf("invalid filename: path traversal detected")
	}
	
	// Check for null bytes (security vulnerability)
	if strings.Contains(filename, "\x00") {
		s.securityLogger.Printf("SECURITY_VIOLATION: Null byte injection attempt in filename: %s", filename)
		return fmt.Errorf("invalid filename: null byte detected")
	}
	
	// Check filename length (prevent buffer overflow attempts)
	if len(filename) > 255 {
		s.securityLogger.Printf("SECURITY_VIOLATION: Filename too long (%d chars): %s", len(filename), filename)
		return fmt.Errorf("invalid filename: filename too long (max 255 characters)")
	}
	
	// Check for dangerous characters
	if s.containsDangerousChars(filename) {
		s.securityLogger.Printf("SECURITY_VIOLATION: Dangerous characters detected in filename: %s", filename)
		return fmt.Errorf("invalid filename: contains dangerous characters")
	}
	
	// Check file extension whitelist
	if !s.hasAllowedExtension(filename) {
		s.securityLogger.Printf("SECURITY_VIOLATION: Invalid file extension in filename: %s (allowed: %v)", filename, s.allowedExtensions)
		return fmt.Errorf("invalid file extension: only %v files are allowed", s.allowedExtensions)
	}
	
	// Check for reserved filenames (Windows specific)
	if s.isReservedFilename(filename) {
		s.securityLogger.Printf("SECURITY_VIOLATION: Reserved filename used: %s", filename)
		return fmt.Errorf("invalid filename: reserved filename")
	}
	
	return nil
}

// IsFileAccessible checks if the file can be accessed
func (s *LogReaderService) IsFileAccessible(filepath string) bool {
	_, err := os.Stat(filepath)
	return err == nil
}

// GetSafeFilePath returns a safe file path within the logs directory with enhanced security checks
func (s *LogReaderService) GetSafeFilePath(filename string) (string, error) {
	if err := s.ValidateFilename(filename); err != nil {
		return "", err
	}
	
	// Clean the filename to remove any remaining dangerous elements
	cleanFilename := filepath.Clean(filename)
	if cleanFilename != filename {
		s.securityLogger.Printf("SECURITY_WARNING: Filename was cleaned from '%s' to '%s'", filename, cleanFilename)
	}
	
	safePath := filepath.Join(s.logDir, cleanFilename)
	
	// Ensure the path is within the logs directory (prevent directory traversal)
	absLogDir, err := filepath.Abs(s.logDir)
	if err != nil {
		s.securityLogger.Printf("SECURITY_ERROR: Failed to get absolute log directory path: %v", err)
		return "", fmt.Errorf("failed to get absolute log directory path: %w", err)
	}
	
	absSafePath, err := filepath.Abs(safePath)
	if err != nil {
		s.securityLogger.Printf("SECURITY_ERROR: Failed to get absolute file path for '%s': %v", filename, err)
		return "", fmt.Errorf("failed to get absolute file path: %w", err)
	}
	
	// Ensure the resolved path is still within the logs directory
	if !strings.HasPrefix(absSafePath, absLogDir+string(filepath.Separator)) && absSafePath != absLogDir {
		s.securityLogger.Printf("SECURITY_VIOLATION: Path traversal attempt - file path '%s' is outside logs directory '%s'", absSafePath, absLogDir)
		return "", fmt.Errorf("file path is outside of logs directory")
	}
	
	// Additional check: ensure no symbolic links are being used to escape the directory
	if s.containsSymlinks(safePath) {
		s.securityLogger.Printf("SECURITY_VIOLATION: Symbolic link detected in path: %s", safePath)
		return "", fmt.Errorf("symbolic links are not allowed")
	}
	
	if !s.IsFileAccessible(safePath) {
		s.auditLogger.Printf("ACCESS_DENIED: File not accessible: %s", filename)
		return "", fmt.Errorf("file is not accessible: %s", filename)
	}
	
	return safePath, nil
}

// Private helper methods

func (s *LogReaderService) incrementReaders() {
	atomic.AddInt32(&s.currentReaders, 1)
}

func (s *LogReaderService) decrementReaders() {
	atomic.AddInt32(&s.currentReaders, -1)
}

func (s *LogReaderService) updateMetrics(duration time.Duration) {
	atomic.AddInt64(&s.requestCount, 1)
	atomic.AddInt64(&s.totalReadTime, int64(duration))
	
	s.metricsMutex.Lock()
	s.metrics.LastUpdateTime = time.Now()
	s.metricsMutex.Unlock()
}

func (s *LogReaderService) hasAllowedExtension(filename string) bool {
	ext := filepath.Ext(filename)
	for _, allowedExt := range s.allowedExtensions {
		if ext == allowedExt {
			return true
		}
	}
	return false
}

// containsPathTraversal checks for various path traversal patterns
func (s *LogReaderService) containsPathTraversal(filename string) bool {
	// Common path traversal patterns
	dangerousPatterns := []string{
		"..",
		"/",
		"\\",
		"./",
		".\\",
		"../",
		"..\\",
		"...",
		"%2e%2e",  // URL encoded ..
		"%2f",     // URL encoded /
		"%5c",     // URL encoded \
		"..%2f",   // Mixed encoding
		"..%5c",   // Mixed encoding
	}
	
	lowerFilename := strings.ToLower(filename)
	for _, pattern := range dangerousPatterns {
		if strings.Contains(lowerFilename, pattern) {
			return true
		}
	}
	
	return false
}

// containsDangerousChars checks for dangerous characters in filename
func (s *LogReaderService) containsDangerousChars(filename string) bool {
	// Dangerous characters that could be used for injection or system manipulation
	dangerousChars := []string{
		"<", ">", ":", "\"", "|", "?", "*",  // Windows reserved chars
		";", "&", "$", "`", "'",             // Shell injection chars
		"\n", "\r", "\t",                    // Control characters
	}
	
	for _, char := range dangerousChars {
		if strings.Contains(filename, char) {
			return true
		}
	}
	
	return false
}

// isReservedFilename checks for Windows reserved filenames
func (s *LogReaderService) isReservedFilename(filename string) bool {
	// Remove extension for checking
	baseName := strings.ToUpper(strings.TrimSuffix(filename, filepath.Ext(filename)))
	
	// Windows reserved names
	reservedNames := []string{
		"CON", "PRN", "AUX", "NUL",
		"COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9",
		"LPT1", "LPT2", "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9",
	}
	
	for _, reserved := range reservedNames {
		if baseName == reserved {
			return true
		}
	}
	
	return false
}

// containsSymlinks checks if the path contains symbolic links
func (s *LogReaderService) containsSymlinks(path string) bool {
	// Check the file itself first
	if info, err := os.Lstat(path); err == nil {
		if info.Mode()&os.ModeSymlink != 0 {
			return true
		}
	}
	
	// For performance reasons, only check the immediate parent directory
	// This provides reasonable security while avoiding deep directory traversal
	dir := filepath.Dir(path)
	if dir != "." && dir != "/" && dir != "\\" && dir != path {
		if info, err := os.Lstat(dir); err == nil {
			if info.Mode()&os.ModeSymlink != 0 {
				return true
			}
		}
	}
	
	return false
}

// logAccessAttempt logs all file access attempts for audit purposes
func (s *LogReaderService) logAccessAttempt(operation, filename, clientIP string, success bool, errorMsg string) {
	if success {
		s.auditLogger.Printf("ACCESS_SUCCESS: %s - File: %s, Client: %s", operation, filename, clientIP)
	} else {
		s.auditLogger.Printf("ACCESS_FAILED: %s - File: %s, Client: %s, Error: %s", operation, filename, clientIP, errorMsg)
	}
}

// logSecurityEvent logs security-related events
func (s *LogReaderService) logSecurityEvent(eventType, filename, clientIP, details string) {
	s.securityLogger.Printf("SECURITY_EVENT: %s - File: %s, Client: %s, Details: %s", eventType, filename, clientIP, details)
}

func (s *LogReaderService) getCurrentMemoryUsage() int64 {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	return int64(m.Alloc)
}

// formatFileSize converts bytes to human readable format
func formatFileSize(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

// readSmallFile reads content from small files (<=10MB)
func (s *LogReaderService) readSmallFile(filePath, filename string, lines, offset int, fileInfo os.FileInfo) (*LogContent, error) {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}
	
	contentStr := string(content)
	allLines := strings.Split(contentStr, "\n")
	totalLines := len(allLines)
	
	// Remove empty last line if it exists (common with log files)
	if totalLines > 0 && allLines[totalLines-1] == "" {
		allLines = allLines[:totalLines-1]
		totalLines = len(allLines)
	}
	
	var startIdx, endIdx int
	
	// If offset is 0, read from the end (tail behavior)
	if offset == 0 {
		// Read the last 'lines' lines
		startIdx = totalLines - lines
		if startIdx < 0 {
			startIdx = 0
		}
		endIdx = totalLines
	} else {
		// Read from specific offset
		startIdx = offset
		if startIdx < 0 {
			startIdx = 0
		}
		if startIdx >= totalLines {
			startIdx = totalLines - 1
		}
		
		endIdx = startIdx + lines
		if endIdx > totalLines {
			endIdx = totalLines
		}
	}
	
	// Extract the requested lines
	selectedLines := allLines[startIdx:endIdx]
	resultContent := strings.Join(selectedLines, "\n")
	
	return &LogContent{
		Filename:     filename,
		Content:      resultContent,
		TotalLines:   totalLines,
		StartLine:    startIdx + 1, // 1-based line numbering
		EndLine:      endIdx,
		FileSize:     fileInfo.Size(),
		LastModified: fileInfo.ModTime(),
	}, nil
}

// readLargeFile reads content from large files (>10MB) using tail strategy
func (s *LogReaderService) readLargeFile(filePath, filename string, lines, offset int, fileInfo os.FileInfo) (*LogContent, error) {
	// For large files, we'll read from the end (tail approach)
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()
	
	// Read the last portion of the file
	const bufferSize = 64 * 1024 // 64KB buffer
	fileSize := fileInfo.Size()
	
	// Calculate how much to read from the end
	readSize := int64(bufferSize)
	if fileSize < readSize {
		readSize = fileSize
	}
	
	// Seek to the position to start reading
	seekPos := fileSize - readSize
	if seekPos < 0 {
		seekPos = 0
	}
	
	_, err = file.Seek(seekPos, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to seek file: %w", err)
	}
	
	buffer := make([]byte, readSize)
	n, err := file.Read(buffer)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}
	
	content := string(buffer[:n])
	allLines := strings.Split(content, "\n")
	
	// Remove the first partial line if we didn't start from the beginning
	if seekPos > 0 && len(allLines) > 0 {
		allLines = allLines[1:]
	}
	
	totalLines := len(allLines)
	
	// Apply line limits
	startIdx := 0
	if offset > 0 && offset < totalLines {
		startIdx = offset
	}
	
	endIdx := startIdx + lines
	if endIdx > totalLines {
		endIdx = totalLines
	}
	
	selectedLines := allLines[startIdx:endIdx]
	resultContent := strings.Join(selectedLines, "\n")
	
	return &LogContent{
		Filename:     filename,
		Content:      resultContent,
		TotalLines:   totalLines, // This is approximate for large files
		StartLine:    startIdx + 1,
		EndLine:      endIdx,
		FileSize:     fileInfo.Size(),
		LastModified: fileInfo.ModTime(),
	}, nil
}

// readFileTail reads the tail of a file for real-time updates
func (s *LogReaderService) readFileTail(filePath string, lines int) (string, error) {
	return s.readFileTailEnhanced(filePath, lines)
}

// readFileTailEnhanced implements an efficient file tail reading algorithm
func (s *LogReaderService) readFileTailEnhanced(filePath string, lines int) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()
	
	// Get file size
	fileInfo, err := file.Stat()
	if err != nil {
		return "", fmt.Errorf("failed to get file info: %w", err)
	}
	
	fileSize := fileInfo.Size()
	if fileSize == 0 {
		return "", nil
	}
	
	// Use adaptive buffer size based on requested lines
	bufferSize := s.calculateOptimalBufferSize(lines, fileSize)
	
	// Read from the end of the file using multiple passes if needed
	content, err := s.readTailWithLineCount(file, fileSize, lines, bufferSize)
	if err != nil {
		return "", fmt.Errorf("failed to read file tail: %w", err)
	}
	
	return content, nil
}

// readIncrementalContent reads only the new content added since lastSize
func (s *LogReaderService) readIncrementalContent(filePath string, lastSize, currentSize int64) (string, error) {
	if currentSize <= lastSize {
		return "", nil
	}
	
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()
	
	// Seek to the position where new content starts
	_, err = file.Seek(lastSize, 0)
	if err != nil {
		return "", fmt.Errorf("failed to seek file: %w", err)
	}
	
	// Read the new content
	newContentSize := currentSize - lastSize
	buffer := make([]byte, newContentSize)
	n, err := file.Read(buffer)
	if err != nil {
		return "", fmt.Errorf("failed to read new content: %w", err)
	}
	
	return string(buffer[:n]), nil
}

// readTailWithLineCount reads the tail of a file ensuring we get the requested number of lines
func (s *LogReaderService) readTailWithLineCount(file *os.File, fileSize int64, lines int, bufferSize int64) (string, error) {
	if lines <= 0 {
		return "", nil
	}
	
	// Start with the calculated buffer size
	readSize := bufferSize
	if fileSize < readSize {
		readSize = fileSize
	}
	
	var allContent strings.Builder
	var foundLines []string
	seekPos := fileSize - readSize
	
	for len(foundLines) < lines && seekPos >= 0 {
		// Seek to the current position
		_, err := file.Seek(seekPos, 0)
		if err != nil {
			return "", fmt.Errorf("failed to seek file: %w", err)
		}
		
		// Read the chunk
		buffer := make([]byte, readSize)
		n, err := file.Read(buffer)
		if err != nil {
			return "", fmt.Errorf("failed to read file: %w", err)
		}
		
		content := string(buffer[:n])
		
		// If we have previous content, prepend it
		if allContent.Len() > 0 {
			content = content + allContent.String()
		}
		allContent.Reset()
		allContent.WriteString(content)
		
		// Split into lines
		currentLines := strings.Split(content, "\n")
		
		// Remove the first partial line if we didn't start from the beginning
		if seekPos > 0 && len(currentLines) > 0 {
			currentLines = currentLines[1:]
		}
		
		foundLines = currentLines
		
		// If we have enough lines or reached the beginning of file, break
		if len(foundLines) >= lines || seekPos == 0 {
			break
		}
		
		// Move to read more content from earlier in the file
		seekPos -= bufferSize
		if seekPos < 0 {
			readSize = bufferSize + seekPos // Adjust read size for the last chunk
			seekPos = 0
		}
	}
	
	// Get the last N lines
	totalLines := len(foundLines)
	startIdx := totalLines - lines
	if startIdx < 0 {
		startIdx = 0
	}
	
	selectedLines := foundLines[startIdx:]
	return strings.Join(selectedLines, "\n"), nil
}

// calculateOptimalBufferSize calculates the optimal buffer size based on requested lines and file size
func (s *LogReaderService) calculateOptimalBufferSize(lines int, fileSize int64) int64 {
	// Base buffer size: 8KB
	baseBufferSize := int64(8 * 1024)
	
	// Estimate bytes per line (average log line is ~100 bytes)
	estimatedBytesPerLine := int64(100)
	estimatedSize := int64(lines) * estimatedBytesPerLine
	
	// Use the larger of base buffer size or estimated size, but cap at 64KB
	bufferSize := baseBufferSize
	if estimatedSize > bufferSize {
		bufferSize = estimatedSize
	}
	
	// Cap at 64KB to avoid excessive memory usage
	maxBufferSize := int64(64 * 1024)
	if bufferSize > maxBufferSize {
		bufferSize = maxBufferSize
	}
	
	// Don't read more than the file size
	if bufferSize > fileSize {
		bufferSize = fileSize
	}
	
	return bufferSize
}

// limitLines limits the content to the specified number of lines
func (s *LogReaderService) limitLines(content string, lines int) string {
	if lines <= 0 {
		return ""
	}
	
	allLines := strings.Split(content, "\n")
	if len(allLines) <= lines {
		return content
	}
	
	// Take the last N lines
	startIdx := len(allLines) - lines
	selectedLines := allLines[startIdx:]
	return strings.Join(selectedLines, "\n")
}

// File Watcher Service Methods

// WatchFile starts monitoring a file for changes
func (fws *FileWatcherService) WatchFile(filename string) (<-chan FileChangeEvent, error) {
	fws.mutex.Lock()
	defer fws.mutex.Unlock()
	
	// Check if file is already being watched
	if watcher, exists := fws.watchers[filename]; exists {
		// Add a new subscriber to existing watcher
		subscriber := make(chan FileChangeEvent, 10) // Buffered channel
		watcher.mutex.Lock()
		watcher.subscribers = append(watcher.subscribers, subscriber)
		watcher.mutex.Unlock()
		return subscriber, nil
	}
	
	// Create new watcher
	watcher := &FileWatcherImpl{
		filename:    filename,
		subscribers: make([]chan FileChangeEvent, 0),
	}
	
	// Initialize file state
	if info, err := os.Stat(filename); err == nil {
		watcher.lastSize = info.Size()
		watcher.lastModified = info.ModTime()
	}
	
	// Create subscriber channel
	subscriber := make(chan FileChangeEvent, 10)
	watcher.subscribers = append(watcher.subscribers, subscriber)
	
	// Store watcher
	fws.watchers[filename] = watcher
	
	// Start monitoring goroutine
	go fws.monitorFile(watcher)
	
	return subscriber, nil
}

// UnwatchFile stops monitoring a file for a specific subscriber
func (fws *FileWatcherService) UnwatchFile(filename string, subscriber <-chan FileChangeEvent) {
	fws.mutex.Lock()
	defer fws.mutex.Unlock()
	
	watcher, exists := fws.watchers[filename]
	if !exists {
		return
	}
	
	watcher.mutex.Lock()
	defer watcher.mutex.Unlock()
	
	// Remove subscriber from the list
	for i, sub := range watcher.subscribers {
		if sub == subscriber {
			// Close the channel
			close(sub)
			// Remove from slice
			watcher.subscribers = append(watcher.subscribers[:i], watcher.subscribers[i+1:]...)
			break
		}
	}
	
	// If no more subscribers, remove the watcher
	if len(watcher.subscribers) == 0 {
		delete(fws.watchers, filename)
	}
}

// monitorFile continuously monitors a file for changes
func (fws *FileWatcherService) monitorFile(watcher *FileWatcherImpl) {
	ticker := time.NewTicker(1 * time.Second) // Check every second
	defer ticker.Stop()
	
	for range ticker.C {
		watcher.mutex.RLock()
		if len(watcher.subscribers) == 0 {
			watcher.mutex.RUnlock()
			break // No more subscribers, stop monitoring
		}
		subscribers := make([]chan FileChangeEvent, len(watcher.subscribers))
		copy(subscribers, watcher.subscribers)
		watcher.mutex.RUnlock()
		
		// Check file status
		info, err := os.Stat(watcher.filename)
		if err != nil {
			// File might have been deleted
			if os.IsNotExist(err) {
				event := FileChangeEvent{
					Filename:    watcher.filename,
					EventType:   "deleted",
					OldSize:     watcher.lastSize,
					NewSize:     0,
					OldModified: watcher.lastModified,
					NewModified: time.Time{},
					Timestamp:   time.Now(),
				}
				fws.notifySubscribers(subscribers, event)
			}
			continue
		}
		
		currentSize := info.Size()
		currentModTime := info.ModTime()
		
		// Check for changes
		if currentSize != watcher.lastSize || currentModTime.After(watcher.lastModified) {
			eventType := "modified"
			if currentSize != watcher.lastSize {
				eventType = "size_changed"
			}
			
			event := FileChangeEvent{
				Filename:    watcher.filename,
				EventType:   eventType,
				OldSize:     watcher.lastSize,
				NewSize:     currentSize,
				OldModified: watcher.lastModified,
				NewModified: currentModTime,
				Timestamp:   time.Now(),
			}
			
			// Update watcher state
			watcher.lastSize = currentSize
			watcher.lastModified = currentModTime
			
			// Notify subscribers
			fws.notifySubscribers(subscribers, event)
		}
	}
}

// notifySubscribers sends an event to all subscribers
func (fws *FileWatcherService) notifySubscribers(subscribers []chan FileChangeEvent, event FileChangeEvent) {
	for _, subscriber := range subscribers {
		select {
		case subscriber <- event:
			// Event sent successfully
		default:
			// Channel is full, skip this subscriber to avoid blocking
		}
	}
}

// GetFileChangeStatus checks if a file has changed since the given parameters
func (s *LogReaderService) GetFileChangeStatus(filename string, lastSize int64, lastModified time.Time) (*FileChangeEvent, error) {
	if err := s.ValidateFilename(filename); err != nil {
		return nil, err
	}
	
	filePath, err := s.GetSafeFilePath(filename)
	if err != nil {
		return nil, err
	}
	
	info, err := os.Stat(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return &FileChangeEvent{
				Filename:    filename,
				EventType:   "deleted",
				OldSize:     lastSize,
				NewSize:     0,
				OldModified: lastModified,
				NewModified: time.Time{},
				Timestamp:   time.Now(),
			}, nil
		}
		return nil, fmt.Errorf("failed to get file info: %w", err)
	}
	
	currentSize := info.Size()
	currentModTime := info.ModTime()
	
	// Determine event type
	eventType := "no_change"
	if currentSize != lastSize && currentModTime.After(lastModified) {
		eventType = "size_changed"
	} else if currentModTime.After(lastModified) {
		eventType = "modified"
	} else if currentSize != lastSize {
		eventType = "size_changed"
	}
	
	return &FileChangeEvent{
		Filename:    filename,
		EventType:   eventType,
		OldSize:     lastSize,
		NewSize:     currentSize,
		OldModified: lastModified,
		NewModified: currentModTime,
		Timestamp:   time.Now(),
	}, nil
}

// StartFileWatcher starts monitoring a file and returns a channel for change events
func (s *LogReaderService) StartFileWatcher(filename string) (<-chan FileChangeEvent, error) {
	if err := s.ValidateFilename(filename); err != nil {
		return nil, err
	}
	
	filePath, err := s.GetSafeFilePath(filename)
	if err != nil {
		return nil, err
	}
	
	return s.fileWatcher.WatchFile(filePath)
}

// StopFileWatcher stops monitoring a file for a specific subscriber
func (s *LogReaderService) StopFileWatcher(filename string, subscriber <-chan FileChangeEvent) error {
	if err := s.ValidateFilename(filename); err != nil {
		return err
	}
	
	filePath, err := s.GetSafeFilePath(filename)
	if err != nil {
		return err
	}
	
	s.fileWatcher.UnwatchFile(filePath, subscriber)
	return nil
}