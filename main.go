package main

import (
	"bytes"
	"encoding/binary"
	"flag"
	"fmt"
	"jackpot/api"
	"jackpot/db"
	"jackpot/env"
	"jackpot/filelog"
	"jackpot/items"
	loggerPkg "jackpot/admin/logger"
	"jackpot/net"
	"jackpot/users"
	"os"
	"path/filepath"
	"reflect"
	"runtime"
	"runtime/debug"
	"strings"
	"strconv"
	"sync"
	"time"

	"github.com/gofiber/contrib/websocket"
	"github.com/gofiber/fiber/v2"
	"gopkg.in/ini.v1"
)

var (
	logger          *filelog.FileLogger
	flagMap         sync.Map
	apiManager      *api.Manager
	startTime       time.Time
	logReaderService *loggerPkg.LogReaderService
)

func init() {
	// 定义命令行参数
	flag.BoolVar(&env.Debug, "d", false, "debug mode")
}

func encodeMsg(msgState byte, msgType byte, msgFlag int64) []byte {
	msg := new(bytes.Buffer)
	binary.Write(msg, binary.BigEndian, int32(14))
	binary.Write(msg, binary.BigEndian, msgState)
	binary.Write(msg, binary.BigEndian, msgType)
	binary.Write(msg, binary.BigEndian, msgFlag)
	var body = make(map[string]any, 0)
	body["state"] = msgState
	body["type"] = msgType
	body["flag"] = msgFlag

	if msgState != net.MSG_STATE_ERROR_HEART {
		logger.Log(filelog.WARN, "ws", "", body)
	}
	return msg.Bytes()
}

func encodeMsgWithData(msgState byte, msgType byte, msgFlag int64, msgJackpot int64, msgData []byte) []byte {
	msg := new(bytes.Buffer)
	binary.Write(msg, binary.BigEndian, int32(len(msgData)+14))
	binary.Write(msg, binary.BigEndian, msgState)
	binary.Write(msg, binary.BigEndian, msgType)
	binary.Write(msg, binary.BigEndian, msgFlag)
	binary.Write(msg, binary.BigEndian, msgData)
	var body = make(map[string]any, 0)
	body["state"] = msgState
	body["type"] = msgType
	body["flag"] = msgFlag
	body["jackpot"] = msgJackpot

	if msgState == net.MSG_STATE_OK {
		logger.Log(filelog.INFO, "ws", string(msgData), body)
	} else {
		logger.Log(filelog.WARN, "ws", string(msgData), body)
	}
	return msg.Bytes()
}

func initLogger(cfg *ini.File) error {
	dir := cfg.Section("log").Key("dir").String()
	pattern := cfg.Section("log").Key("pattern").String()
	lv := cfg.Section("log").Key("lv").String()
	nextTime, err := cfg.Section("log").Key("next_time").Int()
	if err != nil {
		return err
	}
	nextSize, err := cfg.Section("log").Key("next_size").Int()
	if err != nil {
		return err
	}

	logger, err = filelog.NewFileLogger(dir, pattern, lv, nextTime, nextSize)
	if err != nil {
		return err
	}
	return nil
}

func startTimer(sec int) {
	ticker := time.NewTicker(time.Duration(sec) * time.Minute)
	defer ticker.Stop()
	for range ticker.C {
		flagMap.Clear()
		logger.Warn("timer", "clear flag map")
	}
}

func main() {
	// 记录服务启动时间
	startTime = time.Now()

	// 解析命令行参数
	flag.Parse()

	env.Arch = runtime.GOARCH

	// 创建一个临时文件用于存储崩溃报告
	timestamp := time.Now().Format("200601")
	fileName := filepath.Join("./", fmt.Sprintf("%s.crash", timestamp))
	f, err := os.OpenFile(fileName, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		panic(err)
	}
	// 设置崩溃输出
	if err = debug.SetCrashOutput(f, debug.CrashOptions{}); err != nil {
		panic(err)
	}

	cfg, err := ini.Load("config.ini")
	if err != nil {
		panic(err)
	}

	env.PlatId = cfg.Section("base").Key("platid").String()
	env.Version = cfg.Section("base").Key("appver").String()

	// 初始化数据库
	addr := cfg.Section("redis").Key("addr").String()
	pass := cfg.Section("redis").Key("password").String()
	dbname, err := cfg.Section("redis").Key("db").Int()
	if err != nil {
		panic(err)
	}
	if err = db.Run(addr, pass, dbname); err != nil {
		panic(err)
	}

	if err = initLogger(cfg); err != nil {
		panic(err)
	}

	// Initialize log reader service
	logDir := cfg.Section("log").Key("dir").String()
	if logDir == "" {
		logDir = "./logs" // Default log directory
	}
	logReaderService = loggerPkg.NewLogReaderService(logDir, 3, 512) // max 3 concurrent, 512MB memory threshold

	if err = users.InitUsers(logger, cfg); err != nil {
		panic(err)
	}

	if err = items.InitItems(logger); err != nil {
		panic(err)
	}

	// 初始化API管理器
	apiManager = api.NewManager()
	if err = apiManager.Initialize(logger, cfg); err != nil {
		panic(err)
	}

	// 所有模块现在通过API管理器统一管理

	flagMap = sync.Map{}
	// 开启定时清理标记
	sec, err := cfg.Section("base").Key("timer").Int()
	if err != nil {
		panic(err)
	}
	go startTimer(sec)

	app := fiber.New()

	// 添加静态文件服务
	app.Static("/", "./static")

	app.Use("/ws", func(c *fiber.Ctx) error {
		if websocket.IsWebSocketUpgrade(c) {
			c.Locals("allowed", true)
			logger.Warn("ws", "client 1 %v", c.IP())
			return c.Next()
		}
		return fiber.ErrUpgradeRequired
	})

	app.Get("/ws/:id", websocket.New(func(c *websocket.Conn) {
		// c.Locals is added to the *websocket.Conn
		// 打印客户端信息
		body := make(map[string]any)
		body["allowed"] = c.Locals("allowed")
		body["platid"] = c.Params("id")
		body["version"] = c.Query("v")
		body["timeout"] = c.Query("timeout")
		body["session"] = c.Cookies("session")
		body["address"] = c.RemoteAddr().String()
		logger.Log(filelog.WARN, "ws", "client 2", body)

		timeoutStr := c.Query("timeout")
		var timeout time.Duration
		if len(timeoutStr) > 0 {
			if t, err := strconv.Atoi(timeoutStr); err != nil {
				logger.Warn("ws", "timeout %s atoi error: %v", timeoutStr, err)
				return
			} else {
				timeout = time.Duration(t) * time.Second
			}
		}

		var (
			mt  int
			msg []byte
			err error
		)
		for {
			if mt, msg, err = c.ReadMessage(); err != nil {
				logger.Error("ws", "read mt:%d err:%v", mt, err)
				break
			}

			if mt != websocket.BinaryMessage {
				logger.Warn("ws", "not binaryMessage err:%v", err)
				continue
			}

			const msgHeadSize = 14

			reader := bytes.NewReader(msg)
			for reader.Len() >= msgHeadSize {
				// 使用 binary.Read 来更安全地解析消息头部信息
				var msgLen int32
				var msgState byte
				var msgType byte
				var msgFlag int64

				err = binary.Read(reader, binary.BigEndian, &msgLen)
				if err != nil {
					logger.Warn("ws", "binary read msgLen err: %v", err)
					break
				}
				err = binary.Read(reader, binary.BigEndian, &msgState)
				if err != nil {
					logger.Warn("ws", "binary read msgState err: %v", err)
					break
				}
				err = binary.Read(reader, binary.BigEndian, &msgType)
				if err != nil {
					logger.Warn("ws", "binary read msgType err: %v", err)
					break
				}
				err = binary.Read(reader, binary.BigEndian, &msgFlag)
				if err != nil {
					logger.Warn("ws", "binary read msgFlag err: %v", err)
					break
				}

				if msgState == net.MSG_STATE_ERROR_HEART {
					var send = encodeMsg(net.MSG_STATE_ERROR_HEART, msgType, msgFlag)
					if err = c.WriteMessage(mt, send); err != nil {
						logger.Error("ws", "write err: %v", err)
					}
					continue
				}

				if int(msgLen) <= msgHeadSize {
					continue
				}

				// 读取消息体
				msgBody := make([]byte, msgLen-msgHeadSize)
				err = binary.Read(reader, binary.BigEndian, &msgBody)
				if err != nil {
					logger.Warn("ws", "binary read msgBody err: %v", err)
					continue
				}

				if body, ok := flagMap.Load(msgFlag); ok {
					logger.Warn("ws", "msg flag repeat: %d", msgFlag)
					var send = encodeMsgWithData(net.MSG_STATE_ERROR_DUPLICATE, msgType, msgFlag, 0, body.([]byte))
					if err := c.WriteMessage(mt, send); err != nil {
						logger.Error("ws", "write err: %v", err)
					}
					continue
				}

				// 使用API管理器处理消息
				done := make(chan net.RequestChannel)

				if !apiManager.ProcessMessage(msgType, msgBody, done) {
					logger.Warn("ws", "no found msg: %d", msgType)
					var send = encodeMsg(net.MSG_STATE_ERROR_UNDIFINE, msgType, msgFlag)
					if err := c.WriteMessage(mt, send); err != nil {
						logger.Error("ws", "write err: %v", err)
					}
					continue
				}

				if timeout > 0 {
					// 创建一个计时器
					timer := time.NewTimer(timeout)

					select {
					case <-timer.C:
						logger.Warn("ws", "Message processing timeout: type(%d) flag(%d)", msgType, msgFlag)
						timer.Stop()
						var send = encodeMsg(net.MSG_STATE_TIMEOUT, msgType, msgFlag)
						if err := c.WriteMessage(mt, send); err != nil {
							logger.Error("ws", "write err: %v", err)
						}
					case d := <-done:
						timer.Stop()
						if d.Code == net.MSG_STATE_OK {
							flagMap.Store(msgFlag, d.Data)
							var send = encodeMsgWithData(d.Code, msgType, msgFlag, d.Jackpot, d.Data)
							if err := c.WriteMessage(mt, send); err != nil {
								logger.Error("ws", "write err: %v", err)
							}
						} else {
							var send = encodeMsg(d.Code, msgType, msgFlag)
							if err := c.WriteMessage(mt, send); err != nil {
								logger.Error("ws", "write err: %v", err)
							}
						}
					}
				} else {
					// 无超时处理，直接等待结果
					d := <-done
					if d.Code == net.MSG_STATE_OK {
						flagMap.Store(msgFlag, d.Data)
						var send = encodeMsgWithData(d.Code, msgType, msgFlag, d.Jackpot, d.Data)
						if err := c.WriteMessage(mt, send); err != nil {
							logger.Error("ws", "write err: %v", err)
						}
					} else {
						var send = encodeMsg(d.Code, msgType, msgFlag)
						if err := c.WriteMessage(mt, send); err != nil {
							logger.Error("ws", "write err: %v", err)
						}
					}
				}
			}
		}
	}))

	// Log API endpoints with enhanced security
	app.Get("/api/logs/files", func(c *fiber.Ctx) error {
		clientIP := c.IP()
		files, err := logReaderService.GetLogFilesWithClient(clientIP)
		if err != nil {
			logger.Error("log-api", "failed to get log files from %s: %v", clientIP, err)
			return c.Status(fiber.StatusInternalServerError).JSON(loggerPkg.LogFilesResponse{
				Code:    500,
				Message: fmt.Sprintf("Failed to get log files: %v", err),
				Data:    nil,
			})
		}

		return c.JSON(loggerPkg.LogFilesResponse{
			Code:    0,
			Message: "success",
			Data:    files,
		})
	})

	app.Get("/api/logs/content", func(c *fiber.Ctx) error {
		clientIP := c.IP()
		filename := c.Query("file")
		if filename == "" {
			logger.Warn("log-api", "missing file parameter from %s", clientIP)
			return c.Status(fiber.StatusBadRequest).JSON(loggerPkg.LogContentResponse{
				Code:    400,
				Message: "file parameter is required",
			})
		}

		// Parse lines parameter (default: 100)
		lines := 100
		if linesStr := c.Query("lines"); linesStr != "" {
			if parsedLines, err := strconv.Atoi(linesStr); err == nil && parsedLines > 0 {
				lines = parsedLines
			}
		}

		// Parse offset parameter (default: 0)
		offset := 0
		if offsetStr := c.Query("offset"); offsetStr != "" {
			if parsedOffset, err := strconv.Atoi(offsetStr); err == nil && parsedOffset >= 0 {
				offset = parsedOffset
			}
		}

		content, err := logReaderService.ReadLogContentWithClient(filename, lines, offset, clientIP)
		if err != nil {
			logger.Error("log-api", "failed to read log content for file %s from %s: %v", filename, clientIP, err)
			
			// Determine appropriate HTTP status code based on error
			statusCode := fiber.StatusInternalServerError
			if err.Error() == "system is at capacity, cannot process new requests" {
				statusCode = fiber.StatusTooManyRequests
			} else if err.Error() == "file is not accessible: "+filename || strings.Contains(err.Error(), "no such file or directory") {
				statusCode = fiber.StatusNotFound
			} else if strings.Contains(err.Error(), "path traversal detected") || 
					  strings.Contains(err.Error(), "invalid file extension") ||
					  strings.Contains(err.Error(), "dangerous characters") ||
					  strings.Contains(err.Error(), "null byte detected") ||
					  strings.Contains(err.Error(), "reserved filename") {
				statusCode = fiber.StatusBadRequest
			} else if strings.Contains(err.Error(), "rate limit exceeded") {
				statusCode = fiber.StatusTooManyRequests
			}

			return c.Status(statusCode).JSON(loggerPkg.LogContentResponse{
				Code:    statusCode,
				Message: err.Error(),
			})
		}

		return c.JSON(loggerPkg.LogContentResponse{
			Code:    0,
			Message: "success",
			Data:    *content,
		})
	})

	app.Get("/api/logs/tail", func(c *fiber.Ctx) error {
		clientIP := c.IP()
		filename := c.Query("file")
		if filename == "" {
			logger.Warn("log-api", "missing file parameter from %s", clientIP)
			return c.Status(fiber.StatusBadRequest).JSON(loggerPkg.LogTailResponse{
				Code:    400,
				Message: "file parameter is required",
			})
		}

		// Parse lines parameter (default: 100)
		lines := 100
		if linesStr := c.Query("lines"); linesStr != "" {
			if parsedLines, err := strconv.Atoi(linesStr); err == nil && parsedLines > 0 {
				lines = parsedLines
			}
		}

		// Parse since parameter (timestamp for incremental updates)
		var since time.Time
		if sinceStr := c.Query("since"); sinceStr != "" {
			if parsedSince, err := time.Parse(time.RFC3339, sinceStr); err == nil {
				since = parsedSince
			}
		}

		// Parse lastSize parameter for incremental updates
		var lastSize int64 = 0
		if lastSizeStr := c.Query("lastSize"); lastSizeStr != "" {
			if parsedSize, err := strconv.ParseInt(lastSizeStr, 10, 64); err == nil && parsedSize >= 0 {
				lastSize = parsedSize
			}
		}

		// Parse lastModified parameter for incremental updates
		var lastModified time.Time
		if lastModStr := c.Query("lastModified"); lastModStr != "" {
			if parsedMod, err := time.Parse(time.RFC3339, lastModStr); err == nil {
				lastModified = parsedMod
			}
		}

		var tailData *loggerPkg.LogTailData
		var err error

		// Use incremental reading if lastSize and lastModified are provided
		if lastSize > 0 && !lastModified.IsZero() {
			tailData, err = logReaderService.TailLogFileIncrementalWithClient(filename, lines, lastSize, lastModified, clientIP)
		} else {
			tailData, err = logReaderService.TailLogFileWithClient(filename, lines, since, clientIP)
		}

		if err != nil {
			logger.Error("log-api", "failed to tail log file %s from %s: %v", filename, clientIP, err)
			
			// Determine appropriate HTTP status code based on error
			statusCode := fiber.StatusInternalServerError
			if err.Error() == "system is at capacity, cannot process new requests" {
				statusCode = fiber.StatusTooManyRequests
			} else if strings.Contains(err.Error(), "file is not accessible") || strings.Contains(err.Error(), "no such file or directory") {
				statusCode = fiber.StatusNotFound
			} else if strings.Contains(err.Error(), "path traversal detected") || 
					  strings.Contains(err.Error(), "invalid file extension") ||
					  strings.Contains(err.Error(), "dangerous characters") ||
					  strings.Contains(err.Error(), "null byte detected") ||
					  strings.Contains(err.Error(), "reserved filename") {
				statusCode = fiber.StatusBadRequest
			} else if strings.Contains(err.Error(), "rate limit exceeded") {
				statusCode = fiber.StatusTooManyRequests
			}

			return c.Status(statusCode).JSON(loggerPkg.LogTailResponse{
				Code:    statusCode,
				Message: err.Error(),
			})
		}

		return c.JSON(loggerPkg.LogTailResponse{
			Code:    0,
			Message: "success",
			Data:    *tailData,
		})
	})

	// Performance monitoring endpoint
	app.Get("/api/logs/metrics", func(c *fiber.Ctx) error {
		detailed := c.Query("detailed") == "true"
		
		if detailed {
			metrics := logReaderService.GetDetailedMetrics()
			return c.JSON(loggerPkg.PerformanceMetricsResponse{
				Code:    0,
				Message: "success",
				Data:    metrics,
			})
		} else {
			metrics := logReaderService.GetMetrics()
			return c.JSON(loggerPkg.PerformanceMetricsResponse{
				Code:    0,
				Message: "success",
				Data: map[string]interface{}{
					"activeReaders":   metrics.ActiveReaders,
					"memoryUsage":     metrics.MemoryUsage,
					"requestCount":    metrics.RequestCount,
					"averageReadTime": metrics.AverageReadTime,
					"lastUpdateTime":  metrics.LastUpdateTime,
				},
			})
		}
	})

	app.Get("/stats/global/:cost", func(c *fiber.Ctx) error {
		cost := c.Params("cost")
		costInt, err := strconv.ParseInt(cost, 10, 64)

		if err != nil {
			return c.JSON(fiber.Map{
				"code":    1,
				"message": "Invalid cost",
			})
		}

		if costInt != net.COST_TYPE_100 && costInt != net.COST_TYPE_1000 && costInt != net.COST_TYPE_10000 {
			return c.JSON(fiber.Map{
				"code":    1,
				"message": "Invalid cost",
			})
		}

		// 从查询参数获取 ver，默认为 v2
		ver := c.Query("ver", users.VER_V2)

		var global = users.GetGlobalStats(costInt, ver)
		return c.JSON(global)
	})

	app.Post("/stats/global/:cost/:key", func(c *fiber.Ctx) error {
		cost := c.Params("cost")
		costInt, err := strconv.ParseInt(cost, 10, 64)
		if err != nil {
			return c.JSON(fiber.Map{
				"code":    1,
				"message": "Invalid cost",
			})
		}

		if costInt != net.COST_TYPE_100 && costInt != net.COST_TYPE_1000 && costInt != net.COST_TYPE_10000 {
			return c.JSON(fiber.Map{
				"code":    1,
				"message": "Invalid cost",
			})
		}

		key := c.Params("key")
		var body struct {
			Value string `json:"value"`
		}
		if err := c.BodyParser(&body); err != nil {
			return c.JSON(fiber.Map{
				"code":    1,
				"message": "Invalid request body",
			})
		}

		// 从查询参数获取 ver，默认为 v2
		ver := c.Query("ver", users.VER_V2)

		// 使用反射检查并更新属性
		var global = users.GetGlobalStats(costInt, ver)
		v := reflect.ValueOf(&global).Elem()
		field := v.Elem().FieldByName(key)
		if !field.IsValid() {
			return c.JSON(fiber.Map{
				"code":    2,
				"message": "Invalid key",
			})
		}

		// 根据字段类型进行赋值
		switch field.Kind() {
		case reflect.Float64:
			if value, err := strconv.ParseFloat(body.Value, 64); err == nil {
				field.SetFloat(value)
			} else {
				return c.JSON(fiber.Map{
					"code":    2,
					"message": "Invalid float value",
				})
			}
		case reflect.Int64:
			if value, err := strconv.ParseInt(body.Value, 10, 64); err == nil {
				field.SetInt(value)
			} else {
				return c.JSON(fiber.Map{
					"code":    2,
					"message": "Invalid int value",
				})
			}
		case reflect.String:
			field.SetString(body.Value)
		default:
			return c.JSON(fiber.Map{
				"code":    2,
				"message": "Unsupported field type",
			})
		}

		users.UpdateGobalStats(global)

		return c.JSON(fiber.Map{
			"code":    0,
			"message": "Updated successfully",
		})
	})

	app.Post("/stats/cleanup", func(c *fiber.Ctx) error {
		totalDeleted, err := users.Cleanup()
		if err != nil {
			logger.Error("redis-cleanup", "scan error: %v", err)
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"code":    500,
				"message": "清理失败",
			})
		}

		logger.Info("redis-cleanup", "成功删除 %d 个key", totalDeleted)
		return c.JSON(fiber.Map{
			"code":    0,
			"message": fmt.Sprintf("清理完成，共删除 %d 个key", totalDeleted),
		})
	})

	// API模块信息端点 - 支持管理员模式
	app.Get("/api/modules", func(c *fiber.Ctx) error {
		// 检查是否为管理员模式
		adminMode := c.Query("admin") == "true"
		detailed := c.Query("detailed") == "true"

		modules := apiManager.GetModuleInfo()
		supportedTypes := apiManager.GetSupportedMessageTypes()

		// 基础响应数据
		responseData := fiber.Map{
			"modules":               modules,
			"supportedMessageTypes": supportedTypes,
			"totalModules":          len(modules),
		}

		// 管理员模式下添加额外信息
		if adminMode {
			// 添加系统信息
			responseData["systemInfo"] = fiber.Map{
				"version":      env.Version,
				"platform":     env.PlatId,
				"architecture": env.Arch,
				"debugMode":    env.Debug,
				"uptime":       time.Since(startTime).String(),
				"startTime":    startTime.Format("2006-01-02 15:04:05"),
			}

			// 添加配置信息
			responseData["configuration"] = fiber.Map{
				"port":          cfg.Section("base").Key("port").String(),
				"timerInterval": cfg.Section("base").Key("timer").String(),
				"logLevel":      cfg.Section("log").Key("lv").String(),
				"redisAddr":     cfg.Section("redis").Key("addr").String(),
			}

			// 添加模块详细状态
			if detailed {
				moduleDetails := make([]fiber.Map, 0, len(modules))
				for _, module := range modules {
					detail := fiber.Map{
						"name":        module.Name,
						"version":     module.Version,
						"messageType": module.MessageType,
						"status":      "active", // 所有注册的模块都是活跃的
						"initialized": true,     // 已初始化的模块
					}

					// 检查消息类型是否被支持
					if apiManager.IsMessageTypeSupported(module.MessageType) {
						detail["processorStatus"] = "registered"
					} else {
						detail["processorStatus"] = "not_registered"
					}

					moduleDetails = append(moduleDetails, detail)
				}
				responseData["moduleDetails"] = moduleDetails
			}

			// 添加运行时统计
			responseData["runtime"] = fiber.Map{
				"activeConnections": 0, // 可以从WebSocket连接池获取
				"processedMessages": 0, // 可以添加计数器
				"errorCount":        0, // 可以添加错误计数器
			}
		}

		return c.JSON(fiber.Map{
			"code":    0,
			"message": "success",
			"data":    responseData,
		})
	})

	logger.Warn("main", "arch:%s, debug:%v", env.Arch, env.Debug)

	port := cfg.Section("base").Key("port").String()
	app.Listen(":" + port)
}
