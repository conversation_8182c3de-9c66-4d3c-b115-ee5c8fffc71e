import { APIManager } from '../core/api.js';
import { DOMUtils } from '../utils/dom.js';
import { Logger } from '../utils/logger.js';
import { Helpers } from '../utils/helpers.js';
import { CONSTANTS } from '../config/constants.js';

// 管理员模式模块
export class AdminModule {
    constructor(options = {}) {
        this.apiManager = options.apiManager;
        this.logger = options.logger || new Logger();
        this.onResult = options.onResult || (() => {});
        
        this.init();
    }

    // 初始化管理员模块
    init() {
        this.bindEvents();
        this.initializeForm();
        this.logger.info('管理员模块初始化完成');
    }

    // 绑定事件
    bindEvents() {
        // 操作类型变化事件
        const operationSelect = DOMUtils.getElementById('adminOperation');
        if (operationSelect) {
            operationSelect.addEventListener('change', () => {
                this.handleOperationChange();
            });
        }

        // 提交按钮事件
        const submitBtn = DOMUtils.getElementById('adminSubmit');
        if (submitBtn) {
            submitBtn.addEventListener('click', () => {
                this.handleSubmit();
            });
        }

        // 表单字段验证事件
        this.bindValidationEvents();
    }

    // 绑定验证事件
    bindValidationEvents() {
        const fields = ['adminMapId', 'adminStatsKey', 'adminStatsValue'];
        
        fields.forEach(fieldId => {
            const field = DOMUtils.getElementById(fieldId);
            if (field) {
                field.addEventListener('blur', () => {
                    this.validateField(fieldId);
                });
                
                field.addEventListener('input', () => {
                    this.clearFieldError(fieldId);
                });
            }
        });
    }

    // 初始化表单
    initializeForm() {
        this.handleOperationChange(); // 初始化字段显示状态
    }

    // 处理操作类型变化
    handleOperationChange() {
        const operationSelect = DOMUtils.getElementById('adminOperation');
        if (!operationSelect) return;

        const operation = operationSelect.value;
        
        // 隐藏所有可选字段
        this.hideAllOptionalFields();
        
        // 根据操作类型显示相应字段
        switch (operation) {
            case 'stats_get':
                this.showField('mapIdGroup');
                break;
                
            case 'stats_set':
                this.showField('mapIdGroup');
                this.showField('statsKeyGroup');
                this.showField('statsValueGroup');
                break;
                
            case 'module_info':
                this.showField('moduleModeGroup');
                break;
                
            case 'cleanup':
                // 清理操作不需要额外参数
                break;
                
            default:
                this.logger.warning(`未知的操作类型: ${operation}`);
        }
    }

    // 隐藏所有可选字段
    hideAllOptionalFields() {
        const optionalFields = ['mapIdGroup', 'statsKeyGroup', 'statsValueGroup', 'moduleModeGroup'];
        optionalFields.forEach(fieldId => {
            this.hideField(fieldId);
        });
    }

    // 显示字段
    showField(fieldId) {
        const field = DOMUtils.getElementById(fieldId);
        if (field) {
            DOMUtils.removeClass(field, 'hidden');
        }
    }

    // 隐藏字段
    hideField(fieldId) {
        const field = DOMUtils.getElementById(fieldId);
        if (field) {
            DOMUtils.addClass(field, 'hidden');
        }
    }

    // 处理表单提交
    async handleSubmit() {
        try {
            // 验证表单
            const validation = this.validateForm();
            if (!validation.valid) {
                this.showValidationErrors(validation.errors);
                return;
            }

            // 显示加载状态
            this.setSubmitButtonState(true);

            // 执行操作
            const result = await this.executeOperation(validation.data);
            
            // 显示结果
            this.onResult(result);
            this.logger.success('管理员操作执行成功', result);

        } catch (error) {
            this.logger.error('管理员操作执行失败:', error.message);
            this.onResult({ error: error.message });
        } finally {
            this.setSubmitButtonState(false);
        }
    }

    // 验证表单
    validateForm() {
        const operationSelect = DOMUtils.getElementById('adminOperation');
        if (!operationSelect) {
            return { valid: false, errors: ['操作类型选择器未找到'] };
        }

        const operation = operationSelect.value;
        const errors = [];
        const data = { operation };

        switch (operation) {
            case 'stats_get':
                const getValidation = this.validateStatsGet();
                if (!getValidation.valid) {
                    errors.push(...getValidation.errors);
                } else {
                    data.mapId = getValidation.data.mapId;
                }
                break;

            case 'stats_set':
                const setValidation = this.validateStatsSet();
                if (!setValidation.valid) {
                    errors.push(...setValidation.errors);
                } else {
                    Object.assign(data, setValidation.data);
                }
                break;

            case 'module_info':
                const moduleValidation = this.validateModuleInfo();
                if (!moduleValidation.valid) {
                    errors.push(...moduleValidation.errors);
                } else {
                    data.mode = moduleValidation.data.mode;
                }
                break;

            case 'cleanup':
                // 清理操作不需要额外验证
                break;

            default:
                errors.push('请选择有效的操作类型');
        }

        return {
            valid: errors.length === 0,
            errors,
            data
        };
    }

    // 验证统计获取操作
    validateStatsGet() {
        const mapIdInput = DOMUtils.getElementById('adminMapId');
        if (!mapIdInput) {
            return { valid: false, errors: ['地图ID输入框未找到'] };
        }

        const mapIdValidation = Helpers.validateInteger(mapIdInput.value, 1);
        if (!mapIdValidation.valid) {
            return { valid: false, errors: [`地图ID: ${mapIdValidation.error}`] };
        }

        // 验证是否为有效的成本层级
        if (!CONSTANTS.COST_TIERS.includes(mapIdValidation.value)) {
            return { 
                valid: false, 
                errors: [`地图ID必须是有效的成本层级: ${CONSTANTS.COST_TIERS.join(', ')}`] 
            };
        }

        return {
            valid: true,
            data: { mapId: mapIdValidation.value }
        };
    }

    // 验证统计设置操作
    validateStatsSet() {
        const errors = [];
        const data = {};

        // 验证地图ID
        const mapIdValidation = this.validateStatsGet();
        if (!mapIdValidation.valid) {
            errors.push(...mapIdValidation.errors);
        } else {
            data.mapId = mapIdValidation.data.mapId;
        }

        // 验证统计键
        const keyInput = DOMUtils.getElementById('adminStatsKey');
        if (!keyInput || !keyInput.value.trim()) {
            errors.push('统计键不能为空');
        } else {
            data.key = keyInput.value.trim();
        }

        // 验证统计值
        const valueInput = DOMUtils.getElementById('adminStatsValue');
        if (!valueInput || !valueInput.value.trim()) {
            errors.push('统计值不能为空');
        } else {
            const valueValidation = Helpers.validateNumber(valueInput.value);
            if (!valueValidation.valid) {
                errors.push(`统计值: ${valueValidation.error}`);
            } else {
                data.value = valueValidation.value;
            }
        }

        return {
            valid: errors.length === 0,
            errors,
            data
        };
    }

    // 验证模块信息操作
    validateModuleInfo() {
        const modeSelect = DOMUtils.getElementById('adminModuleMode');
        if (!modeSelect) {
            return { valid: false, errors: ['查询模式选择器未找到'] };
        }

        const mode = modeSelect.value;
        const validModes = ['basic', 'admin', 'detailed'];
        
        if (!validModes.includes(mode)) {
            return { valid: false, errors: ['请选择有效的查询模式'] };
        }

        return {
            valid: true,
            data: { mode }
        };
    }

    // 执行操作
    async executeOperation(data) {
        switch (data.operation) {
            case 'stats_get':
                return await this.apiManager.getStats(data.mapId);

            case 'stats_set':
                return await this.apiManager.setStats(data.mapId, data.key, data.value);

            case 'module_info':
                return await this.apiManager.getModuleInfo(data.mode);

            case 'cleanup':
                return await this.apiManager.cleanup();

            default:
                throw new Error(`不支持的操作: ${data.operation}`);
        }
    }

    // 验证单个字段
    validateField(fieldId) {
        const field = DOMUtils.getElementById(fieldId);
        if (!field) return;

        let validation = { valid: true };

        switch (fieldId) {
            case 'adminMapId':
                validation = Helpers.validateInteger(field.value, 1);
                if (validation.valid && !CONSTANTS.COST_TIERS.includes(validation.value)) {
                    validation = { 
                        valid: false, 
                        error: `必须是有效的成本层级: ${CONSTANTS.COST_TIERS.join(', ')}` 
                    };
                }
                break;

            case 'adminStatsKey':
                if (!field.value.trim()) {
                    validation = { valid: false, error: '统计键不能为空' };
                }
                break;

            case 'adminStatsValue':
                validation = Helpers.validateNumber(field.value);
                break;
        }

        if (!validation.valid) {
            this.showFieldError(fieldId, validation.error);
        } else {
            this.clearFieldError(fieldId);
        }

        return validation.valid;
    }

    // 显示字段错误
    showFieldError(fieldId, message) {
        const field = DOMUtils.getElementById(fieldId);
        if (!field) return;

        // 添加错误样式
        DOMUtils.addClass(field, 'error');

        // 显示错误消息
        let errorElement = field.parentNode.querySelector('.error-message');
        if (!errorElement) {
            errorElement = DOMUtils.createElement('div', {
                className: 'error-message',
                textContent: message
            });
            field.parentNode.appendChild(errorElement);
        } else {
            DOMUtils.setText(errorElement, message);
        }
    }

    // 清除字段错误
    clearFieldError(fieldId) {
        const field = DOMUtils.getElementById(fieldId);
        if (!field) return;

        // 移除错误样式
        DOMUtils.removeClass(field, 'error');

        // 移除错误消息
        const errorElement = field.parentNode.querySelector('.error-message');
        if (errorElement) {
            errorElement.remove();
        }
    }

    // 显示验证错误
    showValidationErrors(errors) {
        const errorContainer = this.getOrCreateErrorContainer();
        DOMUtils.clearChildren(errorContainer);

        errors.forEach(error => {
            const errorItem = DOMUtils.createElement('div', {
                className: 'validation-error-item',
                textContent: error
            });
            errorContainer.appendChild(errorItem);
        });

        DOMUtils.show(errorContainer);
    }

    // 获取或创建错误容器
    getOrCreateErrorContainer() {
        let container = DOMUtils.getElementById('adminValidationErrors');
        if (!container) {
            container = DOMUtils.createElement('div', {
                id: 'adminValidationErrors',
                className: 'validation-errors hidden'
            });
            
            const form = document.querySelector('#adminMode .admin-form');
            if (form) {
                form.appendChild(container);
            }
        }
        return container;
    }

    // 设置提交按钮状态
    setSubmitButtonState(loading) {
        const submitBtn = DOMUtils.getElementById('adminSubmit');
        if (!submitBtn) return;

        if (loading) {
            submitBtn.disabled = true;
            submitBtn.textContent = '执行中...';
            DOMUtils.addClass(submitBtn, 'loading');
        } else {
            submitBtn.disabled = false;
            submitBtn.textContent = '执行';
            DOMUtils.removeClass(submitBtn, 'loading');
        }
    }

    // 重置表单
    resetForm() {
        const form = document.querySelector('#adminMode .admin-form');
        if (form) {
            form.reset();
        }

        // 清除所有错误
        const errorElements = form.querySelectorAll('.error-message');
        errorElements.forEach(element => element.remove());

        const errorFields = form.querySelectorAll('.error');
        errorFields.forEach(field => DOMUtils.removeClass(field, 'error'));

        // 隐藏验证错误容器
        const errorContainer = DOMUtils.getElementById('adminValidationErrors');
        if (errorContainer) {
            DOMUtils.hide(errorContainer);
        }

        // 重新初始化表单状态
        this.handleOperationChange();
    }

    // 获取表单数据
    getFormData() {
        const operationSelect = DOMUtils.getElementById('adminOperation');
        const mapIdInput = DOMUtils.getElementById('adminMapId');
        const keyInput = DOMUtils.getElementById('adminStatsKey');
        const valueInput = DOMUtils.getElementById('adminStatsValue');
        const modeSelect = DOMUtils.getElementById('adminModuleMode');

        return {
            operation: operationSelect?.value || '',
            mapId: mapIdInput?.value || '',
            key: keyInput?.value || '',
            value: valueInput?.value || '',
            mode: modeSelect?.value || 'basic'
        };
    }

    // 设置表单数据
    setFormData(data) {
        if (data.operation) {
            const operationSelect = DOMUtils.getElementById('adminOperation');
            if (operationSelect) {
                operationSelect.value = data.operation;
                this.handleOperationChange();
            }
        }

        if (data.mapId) {
            const mapIdInput = DOMUtils.getElementById('adminMapId');
            if (mapIdInput) mapIdInput.value = data.mapId;
        }

        if (data.key) {
            const keyInput = DOMUtils.getElementById('adminStatsKey');
            if (keyInput) keyInput.value = data.key;
        }

        if (data.value !== undefined) {
            const valueInput = DOMUtils.getElementById('adminStatsValue');
            if (valueInput) valueInput.value = data.value;
        }

        if (data.mode) {
            const modeSelect = DOMUtils.getElementById('adminModuleMode');
            if (modeSelect) modeSelect.value = data.mode;
        }
    }
}