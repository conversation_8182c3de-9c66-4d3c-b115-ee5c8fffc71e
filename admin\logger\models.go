package logger

import (
	"time"
)

// LogFile represents a log file with its metadata
type LogFile struct {
	Name         string    `json:"name"`
	Size         int64     `json:"size"`
	ModifiedTime time.Time `json:"modified_time"`
	SizeHuman    string    `json:"size_human"`
}

// LogContent represents the content of a log file with metadata
type LogContent struct {
	Filename     string    `json:"filename"`
	Content      string    `json:"content"`
	TotalLines   int       `json:"total_lines"`
	StartLine    int       `json:"start_line"`
	EndLine      int       `json:"end_line"`
	FileSize     int64     `json:"file_size"`
	LastModified time.Time `json:"last_modified"`
}

// LogTailData represents incremental log content for real-time updates
type LogTailData struct {
	Filename     string    `json:"filename"`
	NewContent   string    `json:"new_content"`
	HasNewData   bool      `json:"has_new_data"`
	CurrentSize  int64     `json:"current_size"`
	LastModified time.Time `json:"last_modified"`
}

// LogFileInfo represents detailed information about a log file
type LogFileInfo struct {
	Name         string    `json:"name"`
	Path         string    `json:"path"`
	Size         int64     `json:"size"`
	ModifiedTime time.Time `json:"modified_time"`
	IsAccessible bool      `json:"is_accessible"`
}

// PerformanceMetrics represents system performance metrics for monitoring
type PerformanceMetrics struct {
	ActiveReaders   int32     `json:"active_readers"`
	MemoryUsage     int64     `json:"memory_usage"`
	RequestCount    int64     `json:"request_count"`
	AverageReadTime float64   `json:"average_read_time"`
	LastUpdateTime  time.Time `json:"last_update_time"`
}

// DetailedPerformanceMetrics represents comprehensive performance metrics
type DetailedPerformanceMetrics struct {
	ActiveReaders         int32     `json:"active_readers"`
	MaxConcurrent         int32     `json:"max_concurrent"`
	MemoryUsage           int64     `json:"memory_usage"`
	MemoryUsageHuman      string    `json:"memory_usage_human"`
	MemoryThreshold       int64     `json:"memory_threshold"`
	MemoryThresholdHuman  string    `json:"memory_threshold_human"`
	MemoryUtilization     float64   `json:"memory_utilization"`
	RequestCount          int64     `json:"request_count"`
	ErrorCount            int64     `json:"error_count"`
	RejectedRequests      int64     `json:"rejected_requests"`
	AverageReadTime       float64   `json:"average_read_time"`
	ErrorRate             float64   `json:"error_rate"`
	RejectionRate         float64   `json:"rejection_rate"`
	CurrentRate           float64   `json:"current_rate"`
	MaxRate               float64   `json:"max_rate"`
	LastUpdateTime        time.Time `json:"last_update_time"`
	Uptime                string    `json:"uptime"`
}

// PerformanceMetricsResponse represents the API response for performance metrics
type PerformanceMetricsResponse struct {
	Code    int                 `json:"code"`
	Message string              `json:"message"`
	Data    map[string]interface{} `json:"data"`
}

// FileChangeEvent represents a file change notification
type FileChangeEvent struct {
	Filename     string    `json:"filename"`
	EventType    string    `json:"event_type"` // "modified", "size_changed", "created", "deleted", "no_change"
	OldSize      int64     `json:"old_size"`
	NewSize      int64     `json:"new_size"`
	OldModified  time.Time `json:"old_modified"`
	NewModified  time.Time `json:"new_modified"`
	Timestamp    time.Time `json:"timestamp"`
}

// API Response structures
type LogFilesResponse struct {
	Code    int       `json:"code"`
	Message string    `json:"message"`
	Data    []LogFile `json:"data"`
}

type LogContentResponse struct {
	Code    int        `json:"code"`
	Message string     `json:"message"`
	Data    LogContent `json:"data"`
}

type LogTailResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    LogTailData `json:"data"`
}

type FileChangeResponse struct {
	Code    int             `json:"code"`
	Message string          `json:"message"`
	Data    FileChangeEvent `json:"data"`
}