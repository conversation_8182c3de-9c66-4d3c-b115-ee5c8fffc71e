import { CONSTANTS } from '../config/constants.js';
import { Logger } from '../utils/logger.js';

// WebSocket 连接管理类
export class WebSocketManager {
    constructor(options = {}) {
        this.serverAddress = options.serverAddress || window.location.host;
        this.logger = options.logger || new Logger();
        this.onMessage = options.onMessage || (() => { });
        this.onStatusChange = options.onStatusChange || (() => { });

        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = CONSTANTS.WS.MAX_RECONNECT_ATTEMPTS;
        this.reconnectInterval = CONSTANTS.WS.RECONNECT_INTERVAL;
        this.heartbeatInterval = null;

        this.messageQueue = []; // 离线消息队列
        this.responseCallbacks = new Map(); // 响应回调
        this.messageId = 0;
        this.flagId = Date.now(); // 初始化flagId，确保唯一性
    }

    // 连接WebSocket - 简化版本，基于成功的简单测试
    connect() {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.logger.warning('WebSocket已连接');
            return Promise.resolve();
        }

        // 清理之前的连接
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }

        return new Promise((resolve, reject) => {
            try {
                const platId = '991';
                const wsUrl = `ws://${this.serverAddress}/ws/${platId}`;
                this.logger.debug(`正在连接到: ${wsUrl}`);

                this.ws = new WebSocket(wsUrl);

                this.ws.onopen = () => {
                    this.logger.result('WebSocket连接成功', true);
                    this.isConnected = true;
                    this.reconnectAttempts = 0;
                    this.updateStatus(CONSTANTS.UI_STATES.CONNECTED);

                    // 启动心跳机制
                    this.startHeartbeat();

                    resolve();
                };

                this.ws.onmessage = (event) => {
                    try {
                        this.handleMessage(event);
                    } catch (error) {
                        this.logger.error('处理WebSocket消息失败: ' + error.message);
                    }
                };

                this.ws.onclose = (event) => {
                    if (event.wasClean) {
                        this.logger.info('WebSocket连接已正常关闭');
                    } else {
                        this.logger.warning(`WebSocket连接异常关闭: ${event.code} - ${event.reason}`);
                    }
                    this.isConnected = false;
                    this.updateStatus(CONSTANTS.UI_STATES.DISCONNECTED);
                    this.stopHeartbeat();

                    // 如果不是正常关闭，尝试重连
                    if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
                        this.scheduleReconnect();
                    }
                };

                this.ws.onerror = () => {
                    this.logger.result('WebSocket连接失败', false);
                    this.isConnected = false;
                    this.updateStatus(CONSTANTS.UI_STATES.DISCONNECTED);
                    reject(new Error('WebSocket连接失败'));
                };

            } catch (error) {
                this.logger.error('WebSocket连接失败: ' + error.message);
                reject(error);
            }
        });
    }

    // 设置事件处理器
    setupEventHandlers(resolve, reject) {
        let resolved = false;

        this.logger.debug('设置WebSocket事件处理器...');

        this.ws.onopen = (event) => {
            this.logger.success('WebSocket连接成功');
            this.logger.debug('onopen事件触发:', event);
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.updateStatus(CONSTANTS.UI_STATES.CONNECTED);
            this.processMessageQueue();

            if (!resolved) {
                resolved = true;
                this.logger.debug('调用resolve()');
                resolve();
            }

            // 延迟启动心跳，确保连接稳定
            setTimeout(() => {
                if (this.isConnected) {
                    this.logger.debug('启动心跳机制');
                    this.startHeartbeat();
                }
            }, 2000);
        };

        this.ws.onmessage = (event) => {
            this.logger.debug('收到WebSocket消息:', event);
            this.handleMessage(event);
        };

        this.ws.onclose = (event) => {
            this.logger.warning(`WebSocket连接关闭: ${event.code} - ${event.reason}`);
            this.logger.debug('onclose事件详情:', event);
            this.isConnected = false;
            this.updateStatus(CONSTANTS.UI_STATES.DISCONNECTED);
            this.stopHeartbeat();

            if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
                this.scheduleReconnect();
            }
        };

        this.ws.onerror = (error) => {
            this.logger.error('WebSocket错误: ' + (error.message || '连接失败'));
            this.logger.debug('onerror事件详情:', error);
            console.error('WebSocket onerror:', error);
            console.log('WebSocket readyState:', this.ws.readyState);
            console.log('WebSocket URL:', this.ws.url);
            this.isConnected = false;
            this.updateStatus(CONSTANTS.UI_STATES.DISCONNECTED);

            if (!resolved) {
                resolved = true;
                this.logger.debug('调用reject()');
                reject(new Error('WebSocket连接失败'));
            }
        };

        this.logger.debug('WebSocket事件处理器设置完成');
    }

    // 处理接收到的消息
    handleMessage(event) {
        try {
            let data;

            if (event.data instanceof ArrayBuffer) {
                // 处理二进制消息
                data = this.parseBinaryMessage(event.data);
            } else if (typeof event.data === 'string') {
                // 处理文本消息
                try {
                    data = JSON.parse(event.data);
                } catch (jsonError) {
                    this.logger.warning('JSON解析失败，作为文本处理:', event.data);
                    data = { text: event.data };
                }
            } else if (event.data instanceof Blob) {
                // 处理Blob消息 - 这是服务器发送的二进制数据
                this.logger.debug('收到Blob格式的消息，大小:', event.data.size);
                // 将Blob转换为ArrayBuffer
                event.data.arrayBuffer().then(buffer => {
                    const parsedData = this.parseBinaryMessage(buffer);
                    if (parsedData && this.onMessage) {
                        this.onMessage(parsedData);
                    }
                }).catch(err => {
                    this.logger.error('Blob转ArrayBuffer失败: ' + err.message);
                });
                return; // 异步处理，直接返回
            } else {
                this.logger.warning('收到未知格式的消息:', typeof event.data);
                return;
            }

            this.logger.debug('收到WebSocket消息:', data);

            // 检查是否是响应消息
            if (data && data.messageId && this.responseCallbacks.has(data.messageId)) {
                const callback = this.responseCallbacks.get(data.messageId);
                callback(data);
                this.responseCallbacks.delete(data.messageId);
            } else {
                // 普通消息处理
                if (this.onMessage) {
                    this.onMessage(data);
                }
            }

        } catch (error) {
            this.logger.error('消息处理失败: ' + error.message);
            this.logger.debug('原始消息数据:', event.data);
            // 不要因为消息处理失败就断开连接
        }
    }

    // 解析二进制消息
    parseBinaryMessage(buffer) {
        try {
            const view = new DataView(buffer);

            // 检查消息长度
            if (buffer.byteLength < 14) {
                this.logger.warning('消息长度不足，至少需要14字节的消息头');
                return null;
            }

            // 读取消息头（14字节）
            // 格式: msgLen(4) + msgState(1) + msgType(1) + msgFlag(8)
            const header = {
                msgLen: view.getInt32(0, false),         // big endian
                msgState: view.getUint8(4),
                msgType: view.getUint8(5),
                messageType: view.getUint8(5),           // 添加messageType别名
                msgFlag: view.getBigInt64(6, false),     // big endian
                userId: view.getBigInt64(6, false)       // 将msgFlag作为userId使用
            };



            // 验证消息状态
            if (header.msgState !== 0 && header.msgState !== 5 && header.msgState !== 6) {
                this.logger.warning(`收到错误状态的消息: ${header.msgState}`);
                return {
                    header,
                    data: null,
                    error: `消息状态错误: ${header.msgState}`
                };
            }

            // 特殊处理心跳响应
            if (header.msgType === 0 && header.msgState === 6) {
                this.logger.debug('收到心跳响应');
                return {
                    header,
                    data: null,
                    isHeartbeat: true
                };
            }

            // 读取JSON数据（如果有）
            let data = null;
            if (buffer.byteLength > 14) {
                const jsonData = new TextDecoder().decode(buffer.slice(14));
                try {
                    data = JSON.parse(jsonData);
                } catch (e) {
                    this.logger.warning('JSON解析失败:', jsonData);
                    data = { raw: jsonData };
                }
            }

            return {
                header,
                data
            };
        } catch (error) {
            this.logger.error('解析二进制消息失败: ' + error.message);
            return null;
        }
    }

    // 发送消息 - 简化版本
    send(message) {
        return new Promise((resolve, reject) => {
            if (!this.isConnected) {
                this.logger.warning('WebSocket未连接');
                reject(new Error('WebSocket未连接'));
                return;
            }

            try {
                // 使用二进制格式发送消息
                this.sendBinary(
                    message.type || 1, // 消息类型
                    message.flag || Date.now(), // 消息标识
                    message
                );

                this.logger.debug('发送WebSocket消息:', message);
                resolve();

            } catch (error) {
                this.logger.error('发送消息失败: ' + error.message);
                reject(error);
            }
        });
    }

    // 发送二进制消息
    sendBinary(messageType, userId, data) {
        if (!this.isConnected) {
            throw new Error('WebSocket未连接');
        }

        // 根据test/main.go的逻辑，不同接口有不同的数据格式
        // 数据已经在GameModule中按照test/main.go的逻辑构建好了
        let requestData = data;

        // 特殊处理：心跳消息
        if (messageType === 0) {
            this.flagId++;
            const header = new ArrayBuffer(14);
            const view = new DataView(header);

            view.setInt32(0, 14, false);           // msgLen = 14 (只有头部)
            view.setUint8(4, 6);                   // msgState = 6 (MSG_STATE_ERROR_HEART)
            view.setUint8(5, 0);                   // msgType = 0
            view.setBigInt64(6, BigInt(this.flagId), false); // msgFlag

            this.ws.send(header);
            this.logger.debug('发送心跳消息');
            return;
        }

        const jsonString = JSON.stringify(requestData);
        const jsonBuffer = new TextEncoder().encode(jsonString);

        // 生成唯一的flagId
        this.flagId++;

        // 创建消息头（14字节）
        // 格式: msgLen(4) + msgState(1) + msgType(1) + msgFlag(8)
        const totalLength = 14 + jsonBuffer.length;
        const header = new ArrayBuffer(14);
        const view = new DataView(header);

        view.setInt32(0, totalLength, false);        // msgLen (big endian)
        view.setUint8(4, 0);                         // msgState (0 = normal)
        view.setUint8(5, messageType);               // msgType
        view.setBigInt64(6, BigInt(this.flagId), false); // msgFlag (big endian)

        // 合并头部和数据
        const message = new Uint8Array(header.byteLength + jsonBuffer.byteLength);
        message.set(new Uint8Array(header), 0);
        message.set(jsonBuffer, header.byteLength);

        this.ws.send(message.buffer);
        this.logger.debug('发送二进制消息:', {
            messageType,
            userId,
            flagId: this.flagId,
            dataSize: jsonBuffer.length,
            data: requestData
        });
    }

    // 处理离线消息队列
    processMessageQueue() {
        while (this.messageQueue.length > 0) {
            const { message, expectResponse, resolve, reject } = this.messageQueue.shift();
            this.send(message, expectResponse).then(resolve).catch(reject);
        }
    }

    // 开始心跳
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected) {
                // 发送心跳消息，使用特殊的心跳消息状态
                try {
                    this.flagId++;
                    const header = new ArrayBuffer(14);
                    const view = new DataView(header);

                    view.setInt32(0, 14, false);           // msgLen = 14 (只有头部)
                    view.setUint8(4, 6);                   // msgState = 6 (MSG_STATE_ERROR_HEART)
                    view.setUint8(5, 0);                   // msgType = 0
                    view.setBigInt64(6, BigInt(this.flagId), false); // msgFlag

                    this.ws.send(header);
                    this.logger.debug('发送心跳消息');
                } catch (error) {
                    this.logger.warning('心跳发送失败:', error.message);
                }
            }
        }, CONSTANTS.WS.HEARTBEAT_INTERVAL);
    }

    // 停止心跳
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    // 安排重连
    scheduleReconnect() {
        this.reconnectAttempts++;
        this.logger.warning(`${this.reconnectInterval / 1000}秒后尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

        setTimeout(() => {
            this.connect().catch(() => {
                // 重连失败，继续尝试
            });
        }, this.reconnectInterval);
    }

    // 断开连接
    disconnect() {
        if (this.ws) {
            this.ws.close(1000, '用户主动断开');
            this.ws = null;
        }
        this.isConnected = false;
        this.stopHeartbeat();
        this.updateStatus(CONSTANTS.UI_STATES.DISCONNECTED);
        this.logger.system('WebSocket连接已断开');
    }

    // 更新连接状态
    updateStatus(status) {
        this.onStatusChange(status);
    }

    // 获取连接状态
    getStatus() {
        if (!this.ws) return CONSTANTS.UI_STATES.DISCONNECTED;

        switch (this.ws.readyState) {
            case WebSocket.CONNECTING:
                return CONSTANTS.UI_STATES.CONNECTING;
            case WebSocket.OPEN:
                return CONSTANTS.UI_STATES.CONNECTED;
            case WebSocket.CLOSING:
            case WebSocket.CLOSED:
            default:
                return CONSTANTS.UI_STATES.DISCONNECTED;
        }
    }
}